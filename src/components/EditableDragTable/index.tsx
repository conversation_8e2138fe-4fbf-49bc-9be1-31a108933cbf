import type { ProColumns } from '@ant-design/pro-components';
import { DragSortTable } from '@ant-design/pro-components';
import { arrayMove } from '@dnd-kit/sortable';
import { useIntl } from '@umijs/max';
import { Form } from 'antd';
import React, { memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';

export interface EditableDragTableProps {
    columns: ProColumns<any>[];
    dataSource?: any[];
    loading?: boolean;
    onDataChange?: (data: any[]) => void;
    rowKey?: string;
    dragSortKey?: string;
    maxHeight?: number;
    readonly?: boolean;
    onValuesChange?: (allValues: any) => void;
}

export interface EditableDragTableRef {
    validateAndGetData: () => Promise<{ success: boolean; data?: any[]; errors?: any[] }>;
    getSelectedRows: () => any[];
    clearSelection: () => void;
    resetData: (data: any[]) => void;
}

const EditableDragTable = memo(React.forwardRef<EditableDragTableRef, EditableDragTableProps>(
    ({ columns, dataSource = [], loading = false, onDataChange, rowKey = 'id', dragSortKey = 'sortOrder', maxHeight = 600, readonly = false, onValuesChange, ...tableProps }, ref) => {
        const intl = useIntl();
        const [form] = Form.useForm();
        const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
        const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
        const editingRowsRef = useRef<Map<React.Key, any>>(new Map());

        // Memoize table data to prevent re-renders from new dataSource array instances
        const tableData = useMemo(() => {
            return dataSource.map((item, index) => ({
                ...item,
                [rowKey]: item[rowKey] || `row_${index}`,
            }));
        }, [dataSource, rowKey]);

        // Set all rows to be editable when data changes
        useEffect(() => {
            const newEditableKeys = tableData.map(item => item[rowKey]).filter(key => key !== undefined);
            setEditableRowKeys(newEditableKeys);
        }, [tableData, rowKey]);


        // Handle row data changes
        const handleValuesChange = useCallback((changedValues: any, allValues: any) => {
            const { [rowKey]: recordKey, ...values } = changedValues;
            if (recordKey) {
                editingRowsRef.current.set(recordKey, values);
            }
            onValuesChange?.(allValues);
        }, [onValuesChange, rowKey]);

        // Handle drag and drop sorting
        const handleDragSortEnd = useCallback((oldIndex: number, newIndex: number) => {
            if (oldIndex !== newIndex) {
                const newItems = arrayMove(dataSource, oldIndex, newIndex).map((item, idx) => ({
                    ...item,
                    [dragSortKey]: idx + 1, // Update sort order field
                }));
                onDataChange?.(newItems);
            }
        }, [dataSource, onDataChange, dragSortKey]);

        // Handle row selection changes
        const handleSelectionChange = useCallback((keys: React.Key[], rows: any[]) => {
            setSelectedRowKeys(keys);
        }, []);

        // Expose methods to parent component
        useImperativeHandle(ref, () => ({
            async validateAndGetData() {
                try {
                    await form.validateFields();
                    // Merge original data with edited data
                    const finalData = tableData.map(row => {
                        const editedData = editingRowsRef.current.get(row[rowKey]);
                        return editedData ? { ...row, ...editedData } : row;
                    });

                    return { success: true, data: finalData };
                } catch (error) {
                    console.error('Validation error:', error);
                    return { success: false, errors: [error] };
                }
            },

            getSelectedRows() {
                return tableData.filter(row => selectedRowKeys.includes(row[rowKey]));
            },

            clearSelection() {
                setSelectedRowKeys([]);
            },

            resetData(data: any[]) {
                onDataChange?.(data);
                editingRowsRef.current.clear();
                setSelectedRowKeys([]);
            },
        }), [tableData, selectedRowKeys, rowKey, form, onDataChange]);

        return (
            <Form
                form={form}
                component={false}
                onValuesChange={handleValuesChange}
            >
                <DragSortTable<any>
                    className="drag-sort-table"
                    columns={columns}
                    rowKey={rowKey}
                    dataSource={tableData}
                    loading={loading}
                    editable={{
                        type: 'multiple',
                        editableKeys: readonly ? [] : editableKeys,
                        form,
                        onValuesChange: handleValuesChange,
                    }}
                    rowSelection={readonly ? undefined : {
                        selectedRowKeys,
                        onChange: handleSelectionChange,
                        preserveSelectedRowKeys: true,
                    }}
                    dragSortKey={dragSortKey}
                    onDragSortEnd={handleDragSortEnd}
                    scroll={{
                        y: maxHeight,
                        x: 'max-content'
                    }}
                    pagination={false}
                    size="small"
                    options={false}
                    search={false}
                    toolBarRender={false}
                    // Performance optimizations
                    virtual={tableData.length > 100}
                    tableAlertRender={() => false}
                    {...tableProps}
                />
            </Form>
        );
    }
));

export default EditableDragTable;
