import Synthesis from '@/components/GoodsSearch/components/search/Synthesis';
import { GoodsSearchBizType } from '@/components/GoodsSearch/types/BizType';
import AuthButton from '@/components/common/AuthButton';
import type {
  ActionType,
  EditableFormInstance,
  ProDescriptionsActionType,
} from '@ant-design/pro-components';
import { PageContainer, ProCard, ProDescriptions } from '@ant-design/pro-components';
import { Alert, Modal, Space, Tag, Tooltip } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'umi';
import { StockUpStatusOptions } from '../list/types/StockUpStatus';
import type { DetailResponseEntity, ReplenishListItemEntity } from './types/detail.response.entity';

import LeftTitle from '@/components/LeftTitle';
import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { queryStoreByAccount } from '@/pages/system/user/services';
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import {
  EditableProTable,
  ProForm,
  ProFormDependency,
  ProFormInstance,
  ProFormSelect,
} from '@ant-design/pro-components';
import { plus, times } from 'number-precision';
import { generatePurchaseOrder, queryPurchaseReplenishListPage } from './services';
import { GeneratePurchaseOrderEntity } from './types/GeneratePurchase.entity';

const PurchaseReplenishCard: React.FC<{ suggestionNo: string }> = ({ suggestionNo }) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<ReplenishListItemEntity[]>([]);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const formRef = useRef<ProFormInstance>();
  const tableFormRef = useRef<EditableFormInstance>();
  const actionRef = useRef<ActionType>();

  const selectedDataSource = dataSource.filter((item) => selectedRowKeys.includes(item.id!));

  const [searchParams, setSearchParams] = useState({
    suggestionNo,
  });

  const columns: ProColumns<ReplenishListItemEntity>[] = [
    {
      title: '序号',
      valueType: 'index',
      fixed: 'left',
      width: 40,
      editable: false,
    },
    {
      title: '商品编码',
      dataIndex: 'itemSn',
      width: 100,
      editable: false,
    },
    {
      title: '商品名称',
      dataIndex: 'skuName',
      width: 120,
      editable: false,
    },
    {
      title: 'OE',
      dataIndex: 'oe',
      width: 120,
      editable: false,
    },
    {
      title: '供应商编码',
      dataIndex: 'brandPartNoList',
      render: (_, record) => record.brandPartNoList?.join(','),
      width: 80,
      editable: false,
    },
    {
      title: '商品品牌',
      dataIndex: 'brandName',
      width: 80,
      editable: false,
    },
    {
      title: '商品分类',
      dataIndex: 'categoryName',
      width: 100,
      editable: false,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      width: 50,
      editable: false,
    },
    {
      title: '车型备注',
      dataIndex: 'adaptModel',
      width: 120,
      editable: false,
    },
    {
      title: '仓库',
      dataIndex: 'warehouseName',
      width: 120,
      editable: false,
    },
    {
      title: '销售数量',
      dataIndex: 'saleNum',
      width: 80,
      editable: false,
    },
    {
      title: '本地库存',
      dataIndex: 'localStock',
      width: 80,
      editable: false,
    },
    {
      title: '本地可用库存',
      dataIndex: 'localAvailableStock',
      width: 80,
      editable: false,
    },
    {
      title: '库存上限',
      dataIndex: 'maxStock',
      width: 80,
      editable: false,
    },
    {
      title: '库存下限',
      dataIndex: 'minStock',
      width: 80,
      editable: false,
    },
    {
      title: '采购在途',
      dataIndex: 'purchaseOnTheWay',
      width: 80,
      editable: false,
    },
    {
      title: '调拨在途',
      dataIndex: 'allocateOnTheWay',
      width: 80,
      editable: false,
    },
    {
      title: '近30天销量',
      dataIndex: 'dSales30',
      width: 80,
      editable: false,
    },
    {
      title: '日均销量',
      dataIndex: 'dSalesAvg',
      width: 80,
      editable: false,
    },
    {
      title: '供应商',
      dataIndex: 'supplierId',
      width: 150,
      valueType: 'select',
      fieldProps: (_, { rowIndex }) => {
        return {
          options: dataSource[rowIndex]?.supplierList?.map((item) => ({
            label: item.supplierName,
            value: item.supplierId,
          })),
          style: {
            width: '100%',
          },
        };
      },
      fixed: 'right',
    },
    {
      title: (
        <span>
          建议补货数量
          <Tooltip
            title={
              <div>
                <p>按库存补货：建议补货数量 = 库存上限 - 可用库存 - 采购在途 - 调拨在途</p>
                <p>按销售补货：建议补货数量 = 销售数量 - 采购在途 - 调拨在途</p>
              </div>
            }
          >
            <QuestionCircleOutlined style={{ marginLeft: 4 }} />
          </Tooltip>
        </span>
      ),
      dataIndex: 'replenishNum',
      width: 100,
      editable: false,
      fixed: 'right',
    },
    {
      title: '采购数量',
      dataIndex: 'purchaseNum',
      width: 90,
      valueType: 'digit',
      fieldProps: {
        precision: 0,
        min: 1,
        style: {
          width: '100%',
        },
      },
      fixed: 'right',
    },
    {
      title: '采购价',
      dataIndex: 'purchasePrice',
      width: 90,
      valueType: 'digit',
      fieldProps: {
        precision: 2,
        min: 0,
        style: {
          width: '100%',
        },
      },
      fixed: 'right',
    },
    {
      title: '采购单号',
      dataIndex: 'purchaseNos',
      width: 120,
      editable: false,
      fixed: 'right',
      ellipsis: true,
      renderText: (_, record) => record.purchaseNos?.join(','),
    },
    {
      title: '已补货数量',
      dataIndex: 'alreadyReplenishNum',
      width: 70,
      editable: false,
      fixed: 'right',
    },
  ];

  const handleSubmit = () => {
    setCreateModalVisible(true);
  };

  const handleGeneratePurchase = (values) => {
    const params: GeneratePurchaseOrderEntity = {
      ...values,
      lineCmdList: selectedDataSource.map((item) => ({
        id: item.id,
        itemSn: item.itemSn,
        purchaseNum: item.purchaseNum,
        purchasePrice: item.purchasePrice,
        supplierId: item.supplierId,
        supplierName: item.supplierName,
      })),
      suggestionNo: searchParams.suggestionNo,
    };

    return generatePurchaseOrder(params).then((res) => {
      setCreateModalVisible(false);
      if (!res) return;
      Modal.info({
        title: '采购单生成结果',
        content: (
          <div>
            {res?.failRo && (
              <>
                <div className="text-red-500 mb-4">
                  <ExclamationCircleOutlined className="mr-2" />
                  失败结果
                </div>
                {res.failRo?.errMsg}
              </>
            )}

            {res?.successRo && Boolean(res?.successRo?.purchaseInfoList?.length) && (
              <>
                <div className="text-green-500 mb-4">
                  <CheckCircleOutlined className="mr-2" />
                  成功结果
                </div>
                <div className="mb-4">
                  已经为您生成 {res.successRo?.successCount} 张采购单，可进入采购单管理页面查看
                </div>
                {res?.successRo?.purchaseInfoList?.map((no, index) => (
                  <div key={index}>{no.purchaseNo}</div>
                ))}
              </>
            )}
          </div>
        ),
        okText: '确定',
        width: 500,
        onOk: () => {
          setCreateModalVisible(false);
          setSelectedRowKeys([]);
          setSearchParams({
            ...searchParams,
            // @ts-ignore
            timestamp: Date.now(),
          });
        },
      });
    });
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      tableFormRef.current?.resetFields();
    }, 1000);
    return () => clearTimeout(timer);
  }, [searchParams.timestamp]);

  return (
    <ProCard
      className="mt-4"
      bodyStyle={{ padding: 0 }}
      title={<LeftTitle title="补货建议明细" />}
      actions={
        <div className="flex p-6 justify-between ">
          <div className="flex text-[16px] text-black/[0.85] font-semibold items-center ">
            <div>
              商品总数：
              <span>
                {selectedDataSource.reduce((total, item) => plus(total, item.purchaseNum ?? 0), 0)}
              </span>
            </div>
            <div className="flex items-center  pl-[40px]">
              采购总金额：
              <span className="text-[24px] font-medium text-[#F83431]">
                ￥
                {selectedDataSource
                  .reduce(
                    (total, item) =>
                      plus(total, times(item.purchaseNum ?? 0, item.purchasePrice ?? 0)),
                    0,
                  )
                  .toFixed(2)}
              </span>
            </div>
          </div>
          {/* 该权限与外部采购提交采购单共用 */}
          <AuthButton
            type="primary"
            key="externalSubmit"
            authority="externalSubmit"
            disabled={selectedRowKeys.length === 0}
            onClick={handleSubmit}
          >
            生成采购单
          </AuthButton>
        </div>
      }
    >
      <div className="p-4">
        <Synthesis
          onSearch={(value) =>
            setSearchParams({
              ...value,
              suggestionNo: searchParams.suggestionNo,
              // @ts-ignore
              timestamp: Date.now(),
            })
          }
          bizType={GoodsSearchBizType.ExternalPurchase}
        />
        <EditableProTable<ReplenishListItemEntity>
          rowKey="id"
          editableFormRef={tableFormRef}
          scroll={{ x: 1500, y: window.innerHeight - 600 > 350 ? 350 : window.innerHeight - 600 }}
          virtual={true}
          columns={columns}
          pagination={{
            showQuickJumper: true,
            defaultPageSize: 10,
            showSizeChanger: true,
            onChange: (page, pageSize) => {
              const timer = setTimeout(() => {
                tableFormRef.current?.resetFields();
                clearTimeout(timer);
              }, 1000);
            },
          }}
          actionRef={actionRef}
          params={searchParams}
          request={async (params) => {
            setSelectedRowKeys([]);
            tableFormRef.current?.resetFields();
            const result = await queryPurchaseReplenishListPage(params);
            const dataList = result?.data.map((item) => {
              return {
                ...item,
                purchaseNum: item.replenishNum, // 默认为补货数量
                supplierId: item.supplierList?.[0]?.supplierId, // 默认选择第一个
                supplierName: item.supplierList?.[0]?.supplierName,
                purchasePrice: item.price,
              };
            });
            setDataSource(dataList);
            return {
              data: dataList,
              success: true,
              total: result?.total,
            };
          }}
          tableAlertRender={() => false}
          rowSelection={{
            columnWidth: 48,
            selectedRowKeys,
            onChange: setSelectedRowKeys,
            getCheckboxProps: (record: ReplenishListItemEntity) => {
              return {
                disabled: !record?.purchaseNum || !record?.purchasePrice || !record?.supplierId,
              };
            },
          }}
          onValuesChange={(allValues, changedValues) => {
            dataSource.forEach((item) => {
              if (item.id === changedValues.id) {
                item.purchaseNum = changedValues.purchaseNum;
                item.purchasePrice = changedValues.purchasePrice;
                item.supplierId = changedValues.supplierId;
                item.supplierName = changedValues.supplierName;
              }
            });
            setDataSource([...dataSource]);
          }}
          editable={{
            type: 'multiple',
            editableKeys: dataSource.map((item) => item.id!),
            actionRender: () => [],
          }}
          recordCreatorProps={false}
        />
      </div>

      <Modal
        title="生成采购单"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={520}
      >
        <Alert
          className="mb-4"
          message="生成的采购单结算方式默认为挂账，生成采购单后，结算方式和收货仓库可修改， 采购门店不可修改。"
          type="error"
        />

        <ProForm
          formRef={formRef}
          submitter={{
            searchConfig: {
              submitText: '确定',
              resetText: '取消',
            },
            onReset: () => setCreateModalVisible(false),
            render: (props, doms) => (
              <div style={{ display: 'flex', justifyContent: 'center', gap: 10 }}>{doms}</div>
            ),
          }}
          onFinish={handleGeneratePurchase}
          layout="horizontal"
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 16 }}
        >
          <ProFormSelect
            name="storeId"
            label="采购门店"
            rules={[{ required: true }]}
            width="md"
            request={() =>
              queryStoreByAccount({ status: 1 }).then((stores) => {
                if (stores?.[0]) {
                  formRef.current?.setFieldValue('storeId', stores[0].id);
                  formRef.current?.setFieldValue('storeName', stores[0].name);
                }
                return stores;
              })
            }
            fieldProps={{
              fieldNames: { label: 'name', value: 'id' },
              onChange: (value, option: any) => {
                formRef.current?.setFieldValue('receiveWarehouseId', undefined);
                formRef.current?.setFieldValue('receiveWarehouseName', undefined);
                formRef.current?.setFieldValue('storeName', option?.label);
              },
              style: { width: '100%' },
            }}
          />

          <ProFormDependency name={['storeId']}>
            {({ storeId }) => (
              <ProFormSelect
                label="收货仓库"
                rules={[{ required: true }]}
                name="receiveWarehouseId"
                width="md"
                fieldProps={{
                  fieldNames: { label: 'warehouseName', value: 'id' },
                  onChange: (value, option: any) => {
                    formRef.current?.setFieldValue('receiveWarehouseName', option?.label);
                  },
                  style: { width: '100%' },
                }}
                params={{ storeIdList: [storeId] }}
                request={(params) => {
                  if (params?.storeIdList?.length > 0) {
                    return warehouseList({
                      ...params,
                      state: YesNoStatus.YES,
                    }).then((s) => {
                      if (s?.warehouseSimpleRoList?.[0]) {
                        formRef.current?.setFieldValue(
                          'receiveWarehouseId',
                          s.warehouseSimpleRoList[0].id,
                        );
                        formRef.current?.setFieldValue(
                          'receiveWarehouseName',
                          s.warehouseSimpleRoList[0].warehouseName,
                        );
                      }
                      return s.warehouseSimpleRoList ?? [];
                    });
                  }
                  return Promise.resolve([]);
                }}
              />
            )}
          </ProFormDependency>
        </ProForm>
      </Modal>
    </ProCard>
  );
};

export default (props: { detail: DetailResponseEntity }) => {
  const { detail: suggestionDetail } = props;
  const [searchParams] = useSearchParams();
  const suggestionNo = searchParams.get('suggestionNo');
  const actionRef = useRef<ProDescriptionsActionType>();

  return (
    <PageContainer>
      <ProCard>
        <ProDescriptions
          dataSource={suggestionDetail}
          actionRef={actionRef}
          title={
            <Space>
              <span>{suggestionNo}</span>
              <Tag color={StockUpStatusOptions[suggestionDetail?.suggestionStatus!]?.status}>
                {StockUpStatusOptions[suggestionDetail?.suggestionStatus!]?.text}
              </Tag>
            </Space>
          }
          column={3}
        >
          <ProDescriptions.Item dataIndex="suggestionName" label="补货建议名称" />
          <ProDescriptions.Item dataIndex="createTime" label="创建时间" />
          <ProDescriptions.Item dataIndex="updateTime" label="更新时间" />
          <ProDescriptions.Item dataIndex="ruleInfo" label="补货规则" span={3} />
        </ProDescriptions>
      </ProCard>

      <PurchaseReplenishCard suggestionNo={suggestionNo} />
    </PageContainer>
  );
};
