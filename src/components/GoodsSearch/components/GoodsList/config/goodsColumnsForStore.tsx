import ColumnRender from '@/components/ColumnRender';
import ImageList from '@/components/ImageList';
import { InputColumnRender } from '@/components/PriceLevelRender';
import type { StocksInfoDrawerProps } from '@/components/StocksInfoDrawer';
import MoneyText from '@/components/common/MoneyText';
import { requiredProps } from '@/types/validateRules';
import { MAX_COUNT } from '@/utils/Constants';
import type { ProColumns } from '@ant-design/pro-components';
import { Button, InputNumber, Tooltip } from 'antd';
import type { GoodsMultiEntity } from '../types/goods.multi.entity';
import type { StoreGoodsEntity } from '../types/store.goods.entity';
import { InfoCircleOutlined } from '@ant-design/icons';
import { GoodsEntity } from '@/pages/goods/list/types/GoodsEntity.entity';
import { GoodsDetailDrawerProps, GoodsDetailType } from '@/components/GoodsDetailDrawer';

export interface GoodsColumnsForStoreProps {
  cstId?: string;
  storeId?: string;
  /**
   * 已选商品ID集合
   */
  addedItemSns: string[];
  /**
   * 添加商品事件
   * @param itemList
   */
  handleAdd: (itemList: any[]) => void;
  /**
   * 查看库存
   */
  handleViewStocksInfo: (data: StocksInfoDrawerProps) => void;
  /**
   * 查看商品详情
   */
  setGoodsDrawer: (data: GoodsDetailDrawerProps) => void;

  /**
   * 国际化对象
   */
  intl: any;
}

export const useGoodsColumnsForStore = (props: GoodsColumnsForStoreProps) => {
  const { intl } = props;

  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
      editable: false,
      dataIndex: 'index',
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.itemSn' }),
      dataIndex: 'itemSn',
      width: 100,
      editable: false,
      render: (_text, record) => {
        return (
          <a
            onClick={() => {
              props.setGoodsDrawer({
                visible: true,
                groupIds: record.itemGroupRoList?.map((item: any) => item.id.toString()),
                type: GoodsDetailType.GoodInfo,
                itemId: record.itemId,
                storeId: props.storeId,
                cstId: props.cstId,
                suggestPrice: record.suggestPrice,
                lowPrice: record.lowPrice,
                costPrice: record.costPrice,
              });
            }}
          >
            {record.itemSn}
          </a>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.itemName' }),
      dataIndex: 'images',
      width: 140,
      editable: false,
      render: (_text, record: StoreGoodsEntity) => {
        return <ImageList itemName={record?.itemName} urls={(record as StoreGoodsEntity).images} />;
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.oeNos' }),
      dataIndex: 'oeNos',
      width: 140,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender(text as string[]);
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.brandPartNos' }),
      dataIndex: 'brandPartNos',
      width: 100,
      editable: false,
      render: (text, record) => {
        return (
          <span className="flex gap-1">
            {text}
            {record.realBrandPartNo && (
              <Tooltip
                title={
                  <div>
                    <div>
                      {intl.formatMessage({
                        id: 'goods.createForm.realBrandPartNoLabel',
                      })}
                    </div>
                    <div>{record.realBrandPartNo}</div>
                  </div>
                }
              >
                <InfoCircleOutlined />
              </Tooltip>
            )}
          </span>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.brandName' }),
      dataIndex: 'brandName',
      width: 100,
      editable: false,
      render: (text, record) => {
        return (
          <span className="flex gap-1">
            {text}
            {record.realBrandName && (
              <Tooltip
                title={
                  <div>
                    <div>
                      {intl.formatMessage({
                        id: 'goods.createForm.realBrandLabel',
                      })}
                    </div>
                    <div>{record.realBrandName}</div>
                  </div>
                }
              >
                <InfoCircleOutlined />
              </Tooltip>
            )}
          </span>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.categoryName' }),
      dataIndex: 'categoryName',
      width: 100,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.originRegionName' }),
      dataIndex: 'originRegionName',
      width: 100,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.spec' }),
      dataIndex: 'spec',
      width: 60,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.unitName' }),
      dataIndex: 'unitName',
      width: 50,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.remark' }),
      dataIndex: 'remark',
      width: 120,
      editable: false,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.locationCode' }),
      dataIndex: 'locationCode',
      width: 100,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.adaptModel' }),
      dataIndex: 'adaptModel',
      width: 120,
      editable: false,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.avaNum' }),
      dataIndex: 'avaNum',
      width: 80,
      editable: false,
      render: (text, record: StoreGoodsEntity) => (
        <a
          className="cursor-pointer"
          onClick={() => {
            props.handleViewStocksInfo({
              itemIdList: [record.itemId!],
            });
          }}
        >
          {record.avaNum}
        </a>
      ),
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.inventoryNum' }),
      dataIndex: 'inventoryNum',
      width: 100,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.suggestPrice' }),
      dataIndex: 'suggestPrice',
      width: 80,
      editable: false,
      render: (_text, record: GoodsEntity) => (
        <a
          className="cursor-pointer"
          onClick={() =>
            props.setGoodsDrawer({
              visible: true,
              groupIds: record.itemGroupRoList?.map((item: any) => item.id.toString()),
              type: GoodsDetailType.PriceInfo,
              itemId: record.itemId,
              storeId: props.storeId,
              cstId: props.cstId,
              suggestPrice: record.suggestPrice,
              lowPrice: record.lowPrice,
              costPrice: record.costPrice,
            })
          }
        >
          {record.suggestPrice ? <MoneyText text={record.suggestPrice} /> : '-'}
        </a>
      ),
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.lastSalePrice' }),
      dataIndex: 'lastSalePrice',
      width: 80,
      editable: false,
      render: (text, record: StoreGoodsEntity) => (text ? <MoneyText text={text as string} /> : ''),
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.lowPrice' }),
      dataIndex: 'lowPrice',
      width: 80,
      editable: false,
      render: (text, record: StoreGoodsEntity) => (text ? <MoneyText text={text as string} /> : ''),
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.costPrice' }),
      dataIndex: 'costPrice',
      width: 80,
      editable: false,
      render: (text, record: StoreGoodsEntity) => (text ? <MoneyText text={text as string} /> : ''),
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.grossMargin' }),
      dataIndex: 'grossMargin',
      width: 80,
      renderFormItem: (item, config) => {
        return (
          <InputNumber
            disabled={true}
            style={{ border: 'none', background: 'transparent', color: 'unset' }}
          />
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.price' }),
      dataIndex: 'price',
      width: 100,
      fixed: 'right',
      renderFormItem: (text, config) => {
        return (
          <InputColumnRender
            record={config.record as StoreGoodsEntity}
            addedItemSns={props.addedItemSns}
            cstId={props.cstId}
          />
        );
      },
      formItemProps: (form, { entity }) => {
        return {
          rules: [
            () => ({
              validator(_, value) {
                const lowPrice = (entity as StoreGoodsEntity).lowPrice;
                const costPrice = (entity as StoreGoodsEntity).costPrice;
                if (!value) {
                  return Promise.reject(
                    new Error(intl.formatMessage({ id: 'goods.search.validation.required' })),
                  );
                } else if (value && lowPrice && lowPrice > value) {
                  return Promise.reject(
                    new Error(intl.formatMessage({ id: 'goods.search.validation.belowMinPrice' })),
                  );
                } else if (value && costPrice && costPrice > value) {
                  return Promise.reject(
                    new Error(intl.formatMessage({ id: 'goods.search.validation.belowCostPrice' })),
                  );
                } else {
                  return Promise.resolve();
                }
              },
            }),
          ],
        };
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.number' }),
      dataIndex: 'number',
      width: 100,
      fixed: 'right',
      valueType: 'digit',
      fieldProps: (_, config) => {
        return {
          min: 1,
          max: MAX_COUNT,
          precision: 0,
          step: 1,
          disabled:
            props.addedItemSns?.includes((config.entity as StoreGoodsEntity).itemSn as string) ||
            !props.cstId,
          onPressEnter: () => props.handleAdd([config.entity as StoreGoodsEntity]),
        };
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: intl.formatMessage({ id: 'goods.search.table.action' }),
      width: 60,
      editable: false,
      fixed: 'right',
      align: 'center',
      dataIndex: 'action',
      render: (text, row) => (
        <Button
          className="px-[0]"
          type="link"
          disabled={props.addedItemSns?.includes(row.itemSn) || !props.cstId}
          onClick={() => props.handleAdd([row as StoreGoodsEntity])}
        >
          {intl.formatMessage({ id: 'goods.search.table.add' })}
        </Button>
      ),
    },
  ] as ProColumns<GoodsMultiEntity, 'text'>[];
};
