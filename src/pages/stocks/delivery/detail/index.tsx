import { PageContainer, ProDescriptions } from '@ant-design/pro-components';
import { useIntl, useParams } from '@umijs/max';
import { Card, Image, Spin, Tag } from 'antd';
import { useCallback, useEffect, useState } from 'react';
import { queryDeliveryDetail } from '../services';
import type { DeliveryEntity } from '../types/delivery.entity';
import { DeliveryStateMap, DistributionModeMap } from '../types/delivery.enums';

const DeliveryDetail: React.FC = () => {
    const intl = useIntl();
    const t = (id: string, ...rest: any[]) => intl.formatMessage({ id }, ...rest);
    const { id } = useParams<{ id: string }>();

    const [detail, setDetail] = useState<DeliveryEntity | null>(null);
    const [loading, setLoading] = useState(true);

    const loadDetail = useCallback(async () => {
        if (!id) return;

        setLoading(true);
        try {
            const result = await queryDeliveryDetail({ id });
            setDetail(result);
        } catch (error) {
            console.error('Failed to load delivery detail:', error);
        } finally {
            setLoading(false);
        }
    }, [id]);

    useEffect(() => {
        loadDetail();
    }, [id, loadDetail]);

    if (loading) {
        return (
            <PageContainer>
                <div className="flex justify-center items-center h-64">
                    <Spin size="large" />
                </div>
            </PageContainer>
        );
    }

    if (!detail) {
        return (
            <PageContainer>
                <div className="text-center text-gray-500 py-8">
                    {t('delivery.detail.notFound')}
                </div>
            </PageContainer>
        );
    }

    const renderImages = () => {
        if (!detail.images) return null;

        const imageList = detail.images.split(',').filter(img => img.trim());
        if (imageList.length === 0) return null;

        return (
            <div className="grid grid-cols-3 gap-4">
                {imageList.map((image) => (
                    <Image
                        key={image}
                        src={image}
                        alt="delivery-image"
                        className="w-full h-32 object-cover rounded"
                    />
                ))}
            </div>
        );
    };

    return (
        <PageContainer
            title={t('stocks.delivery.detail.title')}
            subTitle={detail.bizBillNo}
        >
            <div className="space-y-6">
                {/* 基本信息 */}
                <Card title={t('stocks.delivery.detail.basicInfo')}>
                    <ProDescriptions
                        column={2}
                        dataSource={detail}
                        columns={[
                            {
                                title: t('stocks.delivery.detail.bizBillNo'),
                                dataIndex: 'bizBillNo',
                            },
                            {
                                title: t('stocks.delivery.detail.state'),
                                dataIndex: 'state',
                                render: (_, record) => {
                                    const stateInfo = DeliveryStateMap[record.state as keyof typeof DeliveryStateMap];
                                    return stateInfo ? (
                                        <Tag color={stateInfo.status === 'Success' ? 'green' : stateInfo.status === 'Error' ? 'red' : 'blue'}>
                                            {stateInfo.text}
                                        </Tag>
                                    ) : record.stateDesc;
                                },
                            },
                            {
                                title: t('stocks.delivery.detail.billType'),
                                dataIndex: 'billTypeDesc',
                            },
                            {
                                title: t('stocks.delivery.detail.distributionMode'),
                                dataIndex: 'distributionMode',
                                render: (_, record) => {
                                    const modeInfo = DistributionModeMap[record.distributionMode as keyof typeof DistributionModeMap];
                                    return modeInfo ? modeInfo.text : record.distributionMode;
                                },
                            },
                            {
                                title: t('stocks.delivery.detail.deliveryMan'),
                                dataIndex: 'deliveryMan',
                            },
                            {
                                title: t('stocks.delivery.detail.warehouseName'),
                                dataIndex: 'warehouseName',
                            },
                        ]}
                    />
                </Card>

                {/* 配送信息 */}
                <Card title={t('stocks.delivery.detail.deliveryInfo')}>
                    <ProDescriptions
                        column={2}
                        dataSource={detail}
                        columns={[
                            {
                                title: t('stocks.delivery.detail.deliveryTargetName'),
                                dataIndex: 'deliveryTargetName',
                            },
                            {
                                title: t('stocks.delivery.detail.contactName'),
                                dataIndex: 'contactName',
                            },
                            {
                                title: t('stocks.delivery.detail.contactPhone'),
                                dataIndex: 'contactPhone',
                            },
                            {
                                title: t('stocks.delivery.detail.deliveryAddress'),
                                dataIndex: 'deliveryAddress',
                                span: 2,
                            },
                        ]}
                    />
                </Card>

                {/* 时间信息 */}
                <Card title={t('stocks.delivery.detail.timeInfo')}>
                    <ProDescriptions
                        column={2}
                        dataSource={detail}
                        columns={[
                            {
                                title: t('stocks.delivery.detail.beginTime'),
                                dataIndex: 'beginTime',
                            },
                            {
                                title: t('stocks.delivery.detail.expectedArrTime'),
                                dataIndex: 'expectedArrTime',
                            },
                            {
                                title: t('stocks.delivery.detail.finishTime'),
                                dataIndex: 'finishTime',
                            },
                        ]}
                    />
                </Card>

                {/* 配送图片 */}
                {detail.images && (
                    <Card title={t('stocks.delivery.detail.deliveryImages')}>
                        {renderImages()}
                    </Card>
                )}

                {/* 备注信息 */}
                {detail.remark && (
                    <Card title={t('stocks.delivery.detail.remark')}>
                        <div className="text-gray-700">{detail.remark}</div>
                    </Card>
                )}
            </div>
        </PageContainer>
    );
};

export default DeliveryDetail;