import ColumnRender from '@/components/ColumnRender';
import type { PriceInfoDrawerProps } from '@/components/PriceInfoDrawer';
import AuthButton from '@/components/common/AuthButton';
import MoneyText from '@/components/common/MoneyText';
import type { SubmitPurchaseCartRequest } from '@/pages/purchase/platform/types/submit.purchase.cart.request';
import { MAX_COUNT } from '@/utils/Constants';
import { type ProColumns } from '@ant-design/pro-components';
import { HasInventoryStatusOptions } from '../types/HasInventoryStatus';
import { SuggestionGradeOptions } from '../types/SuggestionValueEnum';
import type { SuggestionItemTableEntity } from '../types/detail.response.entity';

export interface PostListTableColumnsProps {
  handleAgainItem: (id: string) => void;
  handleUpdateItem: (item: SubmitPurchaseCartRequest) => void;
  /**
   * 查看价格
   */
  handleViewPriceInfo: (data: PriceInfoDrawerProps) => void;
  handleAddToStore: (item: SuggestionItemTableEntity) => void;
}

export const PostListTableColumns = (props: PostListTableColumnsProps) =>
  [
    {
      title: '序号',
      valueType: 'index',
      fixed: 'left',
      editable: false,
      width: 40,
    },
    {
      title: '商品名称',
      dataIndex: 'skuName',
      width: 140,
      editable: false,
    },
    {
      title: 'OE',
      dataIndex: 'oe',
      width: 160,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: '供应商编码',
      dataIndex: 'brandPartNoList',
      width: 100,
      editable: false,
      render: (text) => {
        return ColumnRender.ArrayColumnRender((text as string[]) ?? []);
      },
    },
    {
      title: '品牌',
      dataIndex: 'brandName',
      width: 140,
      editable: false,
    },
    {
      title: '分类',
      dataIndex: 'categoryName',
      width: 100,
      editable: false,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      width: 50,
      editable: false,
    },
    {
      title: '适用车系',
      dataIndex: 'adaptSeries',
      width: 140,
      editable: false,
    },
    {
      title: '标准适用车型',
      dataIndex: 'adaptCarModel',
      width: 200,
      editable: false,
      renderText: (text) => {
        return text ? ColumnRender.ArrayColumnRender(text.split(',') ?? []) : '';
      },
    },
    {
      title: '备注',
      dataIndex: 'skuRemark',
      width: 200,
      editable: false,
      ellipsis: true,
    },
    {
      title: '本地库存',
      dataIndex: 'localStock',
      width: 60,
      editable: false,
    },
    {
      title: '库存上限',
      dataIndex: 'maxStock',
      width: 100,
      editable: false,
    },
    {
      title: '库存下限',
      dataIndex: 'minStock',
      width: 100,
      editable: false,
    },
    {
      title: '采购在途',
      dataIndex: 'purchaseOnTheWay',
      width: 100,
      editable: false,
    },
    {
      title: '近30天销量',
      dataIndex: 'dSales30',
      width: 100,
      editable: false,
    },
    {
      title: '日均销量',
      dataIndex: 'dSalesAvg',
      width: 100,
      editable: false,
    },
    {
      title: '最小起订量',
      dataIndex: 'minOrderNum',
      width: 100,
      editable: false,
    },
    {
      title: '最小包装量',
      dataIndex: 'minPackNum',
      width: 100,
      editable: false,
    },
    {
      title: '一体系库存',
      dataIndex: 'hasInventory',
      valueEnum: HasInventoryStatusOptions,
      width: 80,
      editable: false,
    },
    {
      title: '采购价',
      dataIndex: 'price',
      width: 100,
      editable: false,
      valueType: 'money',
      render: (text, record) =>
        record.price && record.memberItemId ? (
          <a
            className="cursor-pointer"
            onClick={() =>
              props.handleViewPriceInfo({
                itemId: record.memberItemId,
                itemName: record.skuName,
                hidePriceTab: true,
              })
            }
          >
            <MoneyText text={record.price} />
          </a>
        ) : (
          record.price
        ),
    },
    {
      title: '建议补货等级',
      dataIndex: 'suggestionGrade',
      valueEnum: SuggestionGradeOptions,
      width: 100,
      editable: false,
    },
    {
      title: '建议补货数量',
      dataIndex: 'suggestionNum',
      fixed: 'right',
      width: 100,
      editable: false,
    },
    {
      title: '采购数量',
      dataIndex: 'num',
      editable: true,
      fixed: 'right',
      width: 100,
      valueType: 'digit',
      fieldProps(_, config) {
        return {
          min: 0,
          precision: 0,
          max: MAX_COUNT,
          onChange: (e: any) => {
            props.handleUpdateItem({
              id: config.entity?.id!,
              itemSn: config.entity?.etcNo,
              price: config.entity?.price,
              num: Number(e),
            });
          },
        };
      },
    },
    {
      title: '操作',
      width: 140,
      fixed: 'right',
      editable: false,
      render: (text, record: SuggestionItemTableEntity) =>
        !record.memberItemId && (
          <AuthButton
            authority="stockUpAddStore"
            type="link"
            onClick={() => props.handleAddToStore(record)}
          >
            添加为门店商品
          </AuthButton>
        ),
    },
  ] as ProColumns<SuggestionItemTableEntity>[];
