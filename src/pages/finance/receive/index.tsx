import AuthButton from '@/components/common/AuthButton';
import FunProTable from '@/components/common/FunProTable';
import { queryReceivedPage } from '@/pages/finance/receive/services';
import type { ReceivedDetailModalType } from '@/pages/finance/receive/types/ReceivedDetailModalType';
import { ReceivedStatus, type ReceivedEntity } from '@/pages/finance/receive/types/ReceivedEntity';
import withKeepAlive from '@/wrappers/withKeepAlive';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-components';
import { type ActionType } from '@ant-design/pro-table/lib';
import { history, useIntl } from '@umijs/max';
import { Space } from 'antd';
import { isNumber } from 'lodash';
import { useRef, useState } from 'react';
import { useActivate } from 'react-activation';
import DetailDrawer from './components/DetailDrawer';
import { getTableColumns } from './config/TableColumns';

export const MoneyFomat = (props: { money: number }) => {
  const { money } = props;
  if (!money) return <span>-</span>;
  if (isNumber(money)) {
    return <span>{money.toFixed(2)}</span>;
  }
  return <span>{money}</span>;
};

const FinReceive = () => {
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  // 明细
  const [detailDrawerProps, setDetailDrawerProps] = useState<ReceivedDetailModalType>({
    visible: false,
  });

  useActivate(() => {
    actionRef.current?.reload();
  });

  /**
   * 打开【明细】对话框
   */
  const openDetailDrawer = (record: ReceivedEntity) => {
    setDetailDrawerProps((preModalProps) => ({
      ...preModalProps,
      title: intl.formatMessage({ id: 'finance.receive.detail' }),
      visible: true,
      serialNumber: record.serialNumber,
    }));
  };
  /**
   * 关闭【明细】对话框
   */
  const closeDetailDrawer = () => {
    setDetailDrawerProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      serialNumber: undefined,
    }));
  };

  const preColumns: ProColumns<ReceivedEntity>[] = [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
    },
    {
      title: intl.formatMessage({ id: 'finance.receive.columns.serialNumber' }),
      dataIndex: 'serialNumber',
      width: 120,
      search: false,
      render: (text, record) => <a onClick={() => openDetailDrawer(record)}>{text}</a>,
    },
  ];

  const operateColumns: ProColumns<ReceivedEntity>[] = [
    {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 100,
      fixed: 'right',
      valueType: 'option',
      render: (text, record, _, action) => <Space>
        {
          [ReceivedStatus.PENDING].includes(record?.status as ReceivedStatus) && <a
            key="editable"
            onClick={() => {
              openDetailDrawer(record)
            }}
          >
            {intl.formatMessage({ id: 'common.button.audit' })}
          </a>
        }
        {[ReceivedStatus.DRAFT, ReceivedStatus.ABORTED].includes(record?.status as ReceivedStatus) && <a
          key="editable"
          onClick={() => {
            history.push(`/finance/receive/add?serialNumber=${record?.serialNumber}`);
          }}
        >
          {intl.formatMessage({ id: 'common.button.edit' })}
        </a>}
        {/* {
          [ReceivedStatus.UNPAYMENT].includes(record?.status as ReceivedStatus) && <a
            key="editable"
            onClick={() => {
              openDetailDrawer(record)
            }}
          >
            {intl.formatMessage({ id: 'common.button.receive' })}
          </a>
        } */}
      </Space>,
    },
  ];

  return (
    <PageContainer>
      <FunProTable<ReceivedEntity, any>
        requestPage={queryReceivedPage}
        formRef={formRef}
        options={{ setting: true, density: false, reload: false }}
        actionRef={actionRef}
        columns={[...preColumns, ...getTableColumns(intl, formRef.current), ...operateColumns]}
        scroll={{ x: 'max-content' }}
        search={{
          layout: 'vertical',
        }}
        headerTitle={
          <Space>
            <AuthButton
              type="primary"
              key="create"
              onClick={() => {
                history.push(`/finance/receive/add`);
              }}
            >
              {intl.formatMessage({ id: 'finance.receive.add' })}
            </AuthButton>
          </Space>
        }
      />
      <DetailDrawer {...detailDrawerProps} onCancel={closeDetailDrawer} />
    </PageContainer>
  );
};

export default withKeepAlive(FinReceive);