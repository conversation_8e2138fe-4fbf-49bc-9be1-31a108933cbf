import { PayKind, payKindOptions } from '@/components/PaymentForm/types/PayKind';
import type { CustomerSaveEntity } from '@/pages/customer/list/types/CustomerSaveEntity';
import { queryMemberAccountPage } from '@/pages/finance/customer/services';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { ProFormDependency, ProFormDigit } from '@ant-design/pro-components';
import { ProFormGroup, ProFormList, ProFormSelect } from '@ant-design/pro-form';
import type { FormListFieldData, FormListOperation } from 'antd';
import { ConfigProvider, Tooltip } from 'antd';
import React, { useEffect, useState, useImperativeHandle, forwardRef } from 'react';
import styles from './index.module.scss';
import { defaultTo } from 'lodash';
import { useIntl } from 'umi';

export interface PaymentFormProps {
  cstDetail?: CustomerSaveEntity;
  storeId?: string; // 门店ID
}

const PaymentForm = forwardRef((props: PaymentFormProps, ref) => {
  const { cstDetail, storeId } = props;
  const intl = useIntl();

  const [accountList, setAccountList] = useState<any[]>([]);
  useEffect(() => {
    if (storeId) {
      queryMemberAccountPage({
        belongToStore: [storeId],
      }).then((result) => {
        if (result?.data?.length) {
          setAccountList(
            result.data.map((item) => ({
              label: item.memberAccountName,
              value: item.id,
            })),
          );
        }
      });
    }
  }, [storeId]);

  /**
   * 对外暴露
   */
  useImperativeHandle(
    ref,
    () => {
      return {
        getState() {
          return { accountList };
        },
      };
    },
    [accountList],
  );

  /**
   * 自定义按钮
   * @param field
   * @param action
   * @param defaultActionDom
   * @param count
   */
  const actionRender = (
    field: FormListFieldData,
    action: FormListOperation,
    defaultActionDom: React.ReactNode[],
    count: number,
  ) => {
    const btn = [];
    switch (count) {
      case 1:
        btn.push(
          <Tooltip title="新增一行">
            <PlusOutlined className="cursor-pointer ml-2" onClick={() => action.add()} />
          </Tooltip>,
        );
        break;
      default:
        btn.push(
          <Tooltip title="删除此行">
            <DeleteOutlined
              className="cursor-pointer ml-2"
              onClick={() => action.remove(field.name)}
            />
          </Tooltip>,
        );
        break;
    }
    return btn;
  };

  return (
    <ConfigProvider theme={{ components: { Form: { itemMarginBottom: 8 } } }}>
      <ProFormSelect
        label={<span className="font-semibold">结算方式</span>}
        name="payKind"
        options={payKindOptions(cstDetail?.settle?.credit === true)}
        allowClear={false}
      />
      <ProFormDependency name={['payKind']}>
        {({ payKind }) => {
          if (payKind === PayKind.Credit) {
            return (
              <div className="text-gray-500 availableAmount">
                {intl.formatMessage({ id: 'sales.order.edit.available' })}$
                {defaultTo(cstDetail?.settle?.availableAmount, '-')}
              </div>
            );
          }
          if (payKind === PayKind.Cash) {
            return (
              <ProFormList
                name="payDetailList"
                max={2}
                min={1}
                initialValue={[{}]}
                creatorButtonProps={false}
                actionRender={actionRender}
                className={styles.group}
              >
                <ProFormGroup key="group">
                  <div className="flex">
                    <ProFormSelect
                      name="payeeAcount"
                      options={accountList}
                      placeholder="请选择账户"
                      width={120}
                      allowClear={false}
                    />
                    <ProFormDigit
                      min={0}
                      name="payAmount"
                      fieldProps={{
                        controls: false,
                        precision: 2,
                        addonAfter: '元',
                      }}
                      allowClear={false}
                    />
                  </div>
                </ProFormGroup>
              </ProFormList>
            );
          }
        }}
      </ProFormDependency>
    </ConfigProvider>
  );
});

export default PaymentForm;
