import AuthButton from "@/components/common/AuthButton";
import FunProTable from "@/components/common/FunProTable";
import { accountListQuerySimple } from "@/pages/system/user/services";
import withKeepAlive from "@/wrappers/withKeepAlive";
import { DownOutlined } from "@ant-design/icons";
import { ActionType, PageContainer } from "@ant-design/pro-components";
import { history, useIntl } from "@umijs/max";
import { Dropdown, Popconfirm, Space } from "antd";
import React from "react";
import { ActivityEntity } from "../../types/ActivityDataType";
import { ActivityProgress, ActivityProgressEnum, ActivityStatus, ActivityStatusEnum, ActivityType, ActivityTypeEnum } from "../../types/ActivityEnum";
import { invalidActivity, queryActivityPage, takeEffectActivity } from "./service";




const ActivityList = () => {

    const actionRef = React.useRef<ActionType>();
    const formRef = React.useRef<any>();
    const intl = useIntl();

    const menuProps = {
        items: [
            {
                label: intl.formatMessage({ id: 'shop.activity.activityType.specialPrice' }),
                key: ActivityType.SPECIAL_PRICE
            },
            {
                label: intl.formatMessage({ id: 'shop.activity.activityType.everyFullGift' }),
                key: ActivityType.EVERY_FULL_GIFT
            },
            {
                label: intl.formatMessage({ id: 'shop.activity.activityType.ladderFullGift' }),
                key: ActivityType.LADDER_FULL_GIFT
            },
            {
                label: intl.formatMessage({ id: 'shop.activity.activityType.buyGiftSelf' }),
                key: ActivityType.BUY_GIFT_SELF
            },
            {
                label: intl.formatMessage({ id: 'shop.activity.activityType.suiteItem' }),
                key: ActivityType.SUITE_ITEM
            }
        ],
        onClick: ({ key }: { key: string }) => {
            history.push(`/shop/activity/edit?type=edit&activityType=${key}`);
        },
    };


    return (
        <PageContainer>
            <FunProTable<ActivityEntity, any>
                requestPage={queryActivityPage}
                actionRef={actionRef}
                formRef={formRef}
                headerTitle={
                    <Space>
                        <Dropdown menu={menuProps}>
                            <AuthButton type='primary' authority='addActivity'>
                                <Space>
                                    {intl.formatMessage({ id: 'common.button.add' })}
                                    <DownOutlined />
                                </Space>
                            </AuthButton>
                        </Dropdown>
                    </Space>
                }
                columns={[
                    {
                        title: intl.formatMessage({ id: 'common.column.index' }),
                        valueType: 'index',
                        width: 40,
                        fixed: 'left',
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.activity.list.activityId' }),
                        dataIndex: 'activityId',
                        width: 80,

                    },
                    {
                        title: intl.formatMessage({ id: 'shop.activity.list.activityName' }),
                        dataIndex: 'activityName',
                        width: 120,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.activity.list.activityType' }),
                        dataIndex: 'activityType',
                        width: 80,
                        valueEnum: ActivityTypeEnum,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.activity.list.activityStatus' }),
                        dataIndex: 'activityStatus',
                        width: 80,
                        valueEnum: ActivityStatusEnum,
                    },
                    {
                        title: intl.formatMessage({ id: 'shop.activity.list.activityStep' }),
                        dataIndex: 'activityStep',
                        width: 80,
                        valueEnum: ActivityProgressEnum,
                    },
                    { title: intl.formatMessage({ id: 'shop.activity.list.activityStartTime' }), dataIndex: 'startTime', width: 80, hideInSearch: true },
                    { title: intl.formatMessage({ id: 'shop.activity.list.activityEndTime' }), dataIndex: 'endTime', width: 80, hideInSearch: true },
                    {
                        title: intl.formatMessage({ id: 'shop.activity.list.updater' }),
                        dataIndex: 'updatePerson',
                        width: 80,
                        search: {
                            transform: (value: any) => {
                                return {
                                    updatePerson: value
                                };
                            },
                        },
                        valueType: 'select',
                        fieldProps: {
                            showSearch: true,
                            fieldNames: { label: 'name', value: 'id' },
                        },
                        request: () => accountListQuerySimple({}),
                    },
                    { title: intl.formatMessage({ id: 'shop.activity.list.updateTime' }), dataIndex: 'updateTime', width: 80, hideInSearch: true },
                    { title: intl.formatMessage({ id: 'shop.activity.list.activityRemark' }), dataIndex: 'remark', width: 80, hideInSearch: true, ellipsis: true },
                    {
                        title: intl.formatMessage({ id: 'common.column.operation' }),
                        width: 140,
                        hideInSearch: true,
                        fixed: 'right',
                        render: (text, record) => {
                            const operType = record.activityStatus === ActivityStatus.TAKE_EFFECT ? intl.formatMessage({ id: 'shop.activity.list.operation.disable' }) : intl.formatMessage({ id: 'shop.activity.list.operation.enable' });
                            return <Space>
                                <AuthButton isHref authority="viewActivity"
                                    onClick={() => {
                                        history.push(`/shop/activity/edit?id=${record.activityId}&type=view&activityType=${record.activityType}`);
                                    }}
                                >
                                    {intl.formatMessage({ id: 'common.button.view' })}
                                </AuthButton>
                                {
                                    [ActivityProgress.NOT_START_ACTIVITY, ActivityProgress.IN_ACTIVITY].includes(record.activityStep!) && <Popconfirm
                                        title={intl.formatMessage({ id: 'customer.customerList.table.popconfirm.enableDisable' }, { operType: operType })}
                                        onConfirm={() => {
                                            if (record.activityStatus === ActivityStatus.TAKE_EFFECT) {
                                                invalidActivity({ activityId: record.activityId! }).then(result => {
                                                    if (result) {
                                                        actionRef.current?.reload(true);
                                                    }
                                                })
                                            } else {
                                                takeEffectActivity({ activityId: record.activityId! }).then(result => {
                                                    if (result) {
                                                        actionRef.current?.reload(true);
                                                    }
                                                })
                                            }
                                        }}>
                                        <AuthButton isHref authority="editActivityStatus">
                                            {operType}
                                        </AuthButton>
                                    </Popconfirm>
                                }
                                {
                                    [ActivityProgress.NOT_START_ACTIVITY, ActivityProgress.IN_ACTIVITY].includes(record.activityStep!) && <AuthButton isHref authority="editActivity"
                                        onClick={() => {
                                            history.push(`/shop/activity/edit?id=${record.activityId}&type=edit&activityType=${record.activityType}`);
                                        }}
                                    >
                                        {intl.formatMessage({ id: 'common.button.edit' })}
                                    </AuthButton>
                                }
                                <AuthButton isHref authority="copyActivity"
                                    onClick={() => {
                                        history.push(`/shop/activity/edit?id=${record.activityId}&type=copy&activityType=${record.activityType}`);
                                    }}
                                >
                                    {intl.formatMessage({ id: 'common.button.copy' })}
                                </AuthButton>
                            </Space>
                        },
                    },
                ]}
            />
        </PageContainer>
    );
};

export default withKeepAlive(ActivityList);