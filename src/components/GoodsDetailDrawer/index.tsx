import { ConfigProvider, Drawer, Tabs } from 'antd';
import GoodsInfo from './components/GoodsInfo';
import GroupInfo from './components/GroupInfo';
import { useEffect, useState } from 'react';
import PriceInfo from './components/PriceInfo';
import PurchaseInfo from './components/PurchaseInfo';

export enum GoodsDetailType {
  GoodInfo = 'GoodInfo',
  GeneralGroup = 'GeneralGroup',
  LocalStock = 'LocalStock',
  PriceInfo = 'PriceInfo',
  PurchaseHistory = 'PurchaseHistory',
}

export interface GoodsDetailDrawerProps {
  visible: boolean;
  onClose?: () => void;
  type?: GoodsDetailType;
  itemId?: string;
  groupId?: string;
  groupIds?: string[];
  storeId?: string;
  cstId?: string;
  suggestPrice?: number;
  lowPrice?: number;
  costPrice?: number;
  refresh?: () => void;
}

export default function GoodsDetailDrawer(props: GoodsDetailDrawerProps) {
  const {
    visible = false,
    onClose,
    type = GoodsDetailType.GoodInfo,
    itemId,
    groupId,
    groupIds,
    storeId,
    cstId,
    refresh,
    lowPrice,
    costPrice,
    suggestPrice,
  } = props;

  console.log('GoodsDetailDrawer', props);

  const [activeTab, setActiveTab] = useState<GoodsDetailType>(GoodsDetailType.GoodInfo);

  useEffect(() => {
    if (type) {
      setActiveTab(type);
    }
    return () => {
      setActiveTab(GoodsDetailType.GoodInfo);
    };
  }, [type, visible]);

  const mainTabItems = [
    {
      label: '商品详情',
      key: GoodsDetailType.GoodInfo,
      children: <GoodsInfo itemId={itemId} />,
    },
  ];

  if (groupIds && groupIds.length > 0) {
    mainTabItems.push({
      label: '通用组',
      key: GoodsDetailType.GeneralGroup,
      children: (
        <GroupInfo groupId={groupId} groupIds={groupIds} refresh={refresh} onClose={onClose} />
      ),
    });
  }

  mainTabItems.push({
    label: '本地库存',
    key: GoodsDetailType.LocalStock,
    children: <div>本地库存</div>,
  });

  if (storeId) {
    mainTabItems.push({
      label: '价格信息',
      key: GoodsDetailType.PriceInfo,
      children: (
        <PriceInfo
          itemId={itemId}
          cstId={cstId}
          storeId={storeId}
          lowPrice={lowPrice}
          costPrice={costPrice}
          suggestPrice={suggestPrice}
        />
      ),
    });
  }

  mainTabItems.push({
    label: '采购历史',
    key: GoodsDetailType.PurchaseHistory,
    children: <PurchaseInfo itemId={itemId} />,
  });

  return (
    <Drawer
      classNames={{
        body: 'bg-gray-100 !p-0',
      }}
      width={1200}
      open={visible}
      onClose={onClose}
      title={'商品信息'}
    >
      <ConfigProvider theme={{ components: { Tabs: {} } }}>
        <Tabs
          items={mainTabItems}
          className="px-6 bg-white"
          activeKey={activeTab}
          onChange={(v) => setActiveTab(v as GoodsDetailType)}
        />
      </ConfigProvider>
    </Drawer>
  );
}
