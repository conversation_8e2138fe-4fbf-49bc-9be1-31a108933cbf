import ColumnRender from '@/components/ColumnRender';
import type { StoreGoodsEntity } from '@/components/GoodsSearch/components/GoodsList/types/store.goods.entity';
import ImageList from '@/components/ImageList';
import type { StocksInfoDrawerProps } from '@/components/StocksInfoDrawer';
import MoneyText from '@/components/common/MoneyText';
import type { OrderGoodsROList } from '@/pages/sales/order/list/types/order.list.item.entity';
import { requiredProps } from '@/types/validateRules';
import { MAX_AMOUNT, MAX_COUNT } from '@/utils/Constants';
import { type ProColumns } from '@ant-design/pro-components';
import { Button, InputNumber } from 'antd';
import { GoodsDetailDrawerProps, GoodsDetailType } from '@/components/GoodsDetailDrawer';

export interface SalesGoodsColumnsProps {
  cstId?: string;
  storeId?: string;
  handleDeleteGoodItem: (id: string) => void;
  handleUpdate: (id: string) => void;
  /**
   * 查看库存
   */
  handleViewStocksInfo: (data: StocksInfoDrawerProps) => void;
  /**
   * 查看商品详情
   */
  setGoodsDrawer: (data: GoodsDetailDrawerProps) => void;
  intl: any;
}

export default (props: SalesGoodsColumnsProps) => {
  const { intl } = props;

  return [
    {
      title: intl.formatMessage({ id: 'sales.order.edit.index' }),
      valueType: 'index',
      fixed: 'left',
      editable: false,
      width: 40,
      dataIndex: 'index',
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.productCode' }),
      dataIndex: 'itemSn',
      width: 100,
      search: false,
      editable: false,
      render: (_text, record: StoreGoodsEntity) => {
        return (
          <a
            onClick={() => {
              props.setGoodsDrawer({
                visible: true,
                // @ts-ignore
                groupIds: record.itemGroupRoList?.map((item: any) => item.id.toString()),
                type: GoodsDetailType.GoodInfo,
                itemId: record.itemId,
                storeId: props.storeId,
                cstId: props.cstId,
                suggestPrice: record.suggestPrice,
                lowPrice: record.lowPrice,
                costPrice: record.costPrice,
              });
            }}
          >
            {record.itemSn}
          </a>
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.productName' }),
      dataIndex: 'images',
      width: 140,
      search: false,
      editable: false,
      render: (text, record) => {
        return <ImageList itemName={record.itemName} urls={record.images} />;
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.oe' }),
      editable: false,
      dataIndex: 'oeNo',
      search: false,
      width: 140,
      render: (text, record) => ColumnRender.ArrayColumnRender(record.oeNo?.split(',') as string[]),
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.brandPartNo' }),
      editable: false,
      dataIndex: 'brandPartNo',
      search: false,
      width: 100,
      render: (text, record) =>
        ColumnRender.ArrayColumnRender(record.brandPartNo?.split(',') as string[]),
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.brand' }),
      dataIndex: 'brandName',
      editable: false,
      search: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.category' }),
      editable: false,
      search: false,
      dataIndex: 'categoryName',
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.origin' }),
      dataIndex: 'originRegionName',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.specification' }),
      dataIndex: 'spec',
      search: false,
      editable: false,
      width: 60,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.unit' }),
      dataIndex: 'unitName',
      search: false,
      editable: false,
      width: 50,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.productRemark' }),
      dataIndex: 'remark',
      search: false,
      editable: false,
      width: 120,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.location' }),
      dataIndex: 'locationCode',
      search: false,
      editable: false,
      width: 100,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.vehicleRemark' }),
      dataIndex: 'adaptModel',
      search: false,
      editable: false,
      width: 120,
      ellipsis: true,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.localStock' }),
      dataIndex: 'avaNum',
      search: false,
      editable: false,
      width: 80,
      render: (text, record: StoreGoodsEntity) => (
        <a
          className="cursor-pointer"
          onClick={() => {
            props.handleViewStocksInfo({
              itemIdList: [record.itemId],
            });
          }}
        >
          {record.avaNum}
        </a>
      ),
    },
    {
      title: intl.formatMessage({ id: 'goods.list.table.inventoryNum' }),
      dataIndex: 'inventoryNum',
      width: 100,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.suggestedPrice' }),
      dataIndex: 'origPriceYuan',
      search: false,
      editable: false,
      width: 80,
      render: (text, record: StoreGoodsEntity) => (
        <a
          className="cursor-pointer"
          onClick={() =>
            props.setGoodsDrawer({
              visible: true,
              // @ts-ignore
              groupIds: record.itemGroupRoList?.map((item: any) => item.id.toString()),
              type: GoodsDetailType.PriceInfo,
              itemId: record.itemId,
              storeId: props.storeId,
              cstId: props.cstId,
              suggestPrice: parseInt(record.origPriceYuan),
              lowPrice: record.lowPrice,
              costPrice: record.costPriceYuan,
            })
          }
        >
          <MoneyText text={record.origPriceYuan} />
        </a>
      ),
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.lastSalePrice' }),
      dataIndex: 'lastSalePrice',
      search: false,
      editable: false,
      valueType: 'money',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.lowestPrice' }),
      dataIndex: 'lowPrice',
      search: false,
      editable: false,
      valueType: 'money',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.costPrice' }),
      dataIndex: 'costPriceYuan',
      search: false,
      editable: false,
      valueType: 'money',
      width: 80,
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.grossMargin' }),
      dataIndex: 'grossMargin',
      search: false,
      editable: false,
      width: 80,
      valueType: 'money',
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.salePrice' }),
      dataIndex: 'unitPriceYuan',
      search: false,
      width: 100,
      fixed: 'right',
      renderFormItem: (item, config) => {
        return (
          <InputNumber
            min={0.01}
            controls={false}
            precision={2}
            max={MAX_AMOUNT}
            placeholder={intl.formatMessage({ id: 'sales.order.edit.inputPlaceholder' })}
            onChange={(value) => {
              props.handleUpdate(config.record?.id!);
            }}
          />
        );
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.saleQuantity' }),
      dataIndex: 'saleNum',
      search: false,
      width: 100,
      fixed: 'right',
      valueType: 'digit',
      renderFormItem: (item, config) => {
        return (
          <InputNumber
            min={1}
            precision={0}
            max={MAX_COUNT}
            placeholder={intl.formatMessage({ id: 'sales.order.edit.inputPlaceholder' })}
            onChange={(value) => {
              props.handleUpdate(config.record?.id!);
            }}
          />
        );
      },
      formItemProps: () => {
        return {
          rules: [requiredProps],
        };
      },
    },
    {
      title: intl.formatMessage({ id: 'sales.order.edit.operation' }),
      width: 60,
      editable: false,
      dataIndex: 'action',
      fixed: 'right',
      render: (text, row) => (
        <Button type={'link'} onClick={() => props.handleDeleteGoodItem(row.id)}>
          {intl.formatMessage({ id: 'sales.order.edit.delete' })}
        </Button>
      ),
    },
  ] as ProColumns<OrderGoodsROList>[];
};
