import type { FinPayableEntity } from '@/pages/finance/payment/types/FinPayableEntity';
import type { ProColumns } from '@ant-design/pro-components';
import { ProFormMoney } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';

export interface CreateReceivedOrderDetailColumnsProps {
  handleUpdate: (record: FinPayableEntity) => void;
  intl: IntlShape;
}

export default (props: CreateReceivedOrderDetailColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      fixed: 'left',
      width: 40,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'finance.supplierPayment.columns.supplier' }),
      dataIndex: 'sellerName',
      width: 100,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'finance.supplierPayment.columns.businessOrderNo' }),
      dataIndex: 'orderNo',
      width: 100,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'finance.supplierPayment.columns.businessCompleteTime' }),
      dataIndex: 'billDate',
      width: 140,
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'finance.supplierPayment.columns.orderAmount' }),
      dataIndex: 'orderAmountYuan',
      search: false,
      width: 100,
      editable: false,
      valueType: 'money',
    },
    {
      title: props.intl.formatMessage({ id: 'finance.supplierPayment.columns.paidAmount' }),
      dataIndex: 'paymentAmountYuan',
      search: false,
      width: 100,
      valueType: 'money',
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'finance.supplierPayment.columns.unpaidAmount' }),
      dataIndex: 'remainPayableAmountYuan',
      search: false,
      width: 100,
      valueType: 'money',
      editable: false,
    },
    {
      title: props.intl.formatMessage({ id: 'finance.supplierPayment.columns.currentWriteOff' }),
      dataIndex: 'currPayAmount',
      search: false,
      width: 100,
      editable: false,
      render: (text, record) => {
        return (
          <ProFormMoney
            controls={false}
            precision={2}
            value={record?.currPayAmount}
            prefix="$"
            placeholder={props.intl.formatMessage({ id: 'finance.supplierPayment.placeholders.enterAmount' })}
            max={999999999.99}
            min={-999999999.99}
            onChange={(value) => props.handleUpdate({ ...record, currPayAmount: value })}
          />
        );
      },
    },
  ] as ProColumns<FinPayableEntity>[];
