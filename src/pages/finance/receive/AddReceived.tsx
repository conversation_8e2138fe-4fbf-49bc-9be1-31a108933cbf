import LeftTitle from '@/components/LeftTitle';
import ProFormObject, { ObjectType } from '@/components/ProFormItem/ProFormObject';
import { ProFormUploadSingleCard } from '@/components/ProFormItem/ProFormUpload';
import FunProTable from '@/components/common/FunProTable';
import { queryReceivableList } from "@/pages/finance/collection/services";
import type { FinReceivableEntity } from "@/pages/finance/collection/types/FinReceivableEntity.entity";
import { queryMemberAccountPage } from "@/pages/finance/customer/services";
import { getCreateColumns } from "@/pages/finance/receive/config/CreateColumns";
import { queryStoreByAccount } from '@/pages/system/user/services';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import withKeepAlive from '@/wrappers/withKeepAlive';
import {
  PageContainer,
  ProCard,
  ProForm,
  ProFormDependency,
  ProFormItem,
  ProFormMoney,
  ProFormSelect,
  ProFormText,
  ProFormTextArea
} from '@ant-design/pro-components';
import { history, useIntl, useSearchParams } from '@umijs/max';
import { useUpdateEffect } from 'ahooks';
import { Button, Checkbox, Flex, Form, Input, InputNumber, message, Space } from 'antd';
import _ from 'lodash';
import React, { useEffect, useMemo, useState } from "react";
import { queryAllCurrency } from '../tag/services';
import { CurrencyEntity } from '../tag/types/CurrencyEntity';
import { queryReceivedFlowList, queryTotalReceivable, saveReceived, TotalReceivableEntity, updateReceivedStatus } from './services';
import { AdjustType } from './types/ReceivedConfirmation';
import { ReceivedStatus } from './types/ReceivedEntity';
const { Search } = Input;

const width = 'md';

const localCurrencyTransfer = (amount, rate = 1) => {
  return _.divide(amount, rate);
}

const AddReceived = () => {
  const intl = useIntl();
  const [searchParams] = useSearchParams();
  const serialNumber = searchParams.get('serialNumber') ?? '';
  const [form] = Form.useForm();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [_receivableList, setReceivableList] = useState<FinReceivableEntity[]>([]);
  const [currencySelect, setCurrencySelect] = useState<CurrencyEntity>({
    targetCurrency: 'AUD',
    rate: 1,
    currencySymbol: '$',
  });
  const [accountSelect, setAccountSelect] = useState();
  const [totalReceivable, setTotalReceivable] = useState<TotalReceivableEntity[]>([]);

  const [orderNo, setOrderNo] = useState<string | undefined>(); // 核销订单过滤：业务单号
  const [overdueType, setOverdueType] = useState<('Due' | 'Over Due')[]>([]); // 核销订单过滤：已到期/已到期

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const receivableList = useMemo(() => {
    let filterList = (_receivableList ?? []);
    if (orderNo) filterList = filterList.filter(item => item.orderNo === orderNo);
    if (Boolean(overdueType.length)) return filterList.filter(item => overdueType.includes(item.tag));
    return filterList;
  }, [_receivableList, overdueType, orderNo]);

  // 是否是本位币
  const isLocalCurrency = currencySelect.targetCurrency === 'AUD';
  const isCrossCurrency = currencySelect.targetCurrency !== receivableList.filter(item => selectedRowKeys.includes(item.orderNo))[0]?.currency;

  const buyerId = Form.useWatch('buyerId', form);
  const buyerType = Form.useWatch('buyerType', form);
  // 收款金额
  const watchedTotalReceivedAmountValue = Form.useWatch('totalReceivedAmountYuan', form) ?? 0;
  const localTotalReceivedAmountValue = localCurrencyTransfer(watchedTotalReceivedAmountValue, currencySelect.rate);
  // 调整金额
  const watchedAdjustAmountValue = Form.useWatch('adjustAmount', form) ?? 0;
  const localAdjustAmountValue = localCurrencyTransfer(watchedAdjustAmountValue, currencySelect.rate);
  // 本次核销合计
  const currTotalReceivedAmount = useMemo(() => {
    return receivableList.filter(item => selectedRowKeys.includes(item.orderNo)).reduce((accumulator, currentItem) => {
      return _.add(accumulator, Number(currentItem.currReceivedAmount ?? 0));
    }, 0);
  }, [receivableList, selectedRowKeys, watchedAdjustAmountValue]);

  const localCurrTotalReceivedAmount = useMemo(() => {
    return receivableList.filter(item => selectedRowKeys.includes(item.orderNo)).reduce((accumulator, currentItem) => {
      return _.add(accumulator, localCurrencyTransfer(Number(currentItem.currReceivedAmount ?? 0), currentItem.rate));
    }, 0);
  }, [receivableList, selectedRowKeys]);

  // 转预收金额=收款金额+调整金额-本次核销金额
  const advanceAmount = useMemo(() => {
    if (isCrossCurrency) {
      return 0;
    }
    return _.subtract(_.add(watchedTotalReceivedAmountValue, watchedAdjustAmountValue), currTotalReceivedAmount);
  }, [watchedTotalReceivedAmountValue, watchedAdjustAmountValue, currTotalReceivedAmount, isCrossCurrency]);

  const localAdvanceAmount = localCurrencyTransfer(advanceAmount, currencySelect.rate);

  // 本位币汇率损益=收款金额（按收款汇率折合本位币）+调整金额（按收款汇率折合本位币）-本次核销金额（按应收汇率折合本位币）-转预收金额（按收款汇率折合本位币）
  const localCurrencyLoss = useMemo(() => {
    return _.subtract(_.add(localTotalReceivedAmountValue, localAdjustAmountValue), _.add(localCurrTotalReceivedAmount, localAdvanceAmount));
  }, [localTotalReceivedAmountValue, localAdjustAmountValue, localCurrTotalReceivedAmount, localAdvanceAmount]);


  const getData = async () => {
    queryReceivedFlowList(serialNumber).then(result => {
      const { finReceivedRo = {}, finReceivedFlowRoList = [] } = result || {};

      form.setFieldsValue({
        ...finReceivedRo,
        adjustType: String(finReceivedRo?.adjustType ?? ''),
        buyerType: finReceivedRo?.buyerType === 1 ? ObjectType.Customer : ObjectType.OtherCompany,
        receivePicFileList: finReceivedRo?.receivePic ? [{
          uid: '-1',
          name: 'image.png',
          status: 'done',
          url: finReceivedRo?.receivePic,
        }] : []
      });

      setCurrencySelect({
        targetCurrency: finReceivedRo?.currency,
        rate: finReceivedRo.rate,
        currencySymbol: finReceivedRo?.currencySymbol
      });
      queryReceivableList({
        buyerId: finReceivedRo?.buyerId,
        receivableFlag: 1,
      }).then((result = []) => {
        const filterData = result
          .filter(item => item.remainReceivableAmountYuan != 0).sort((a, b) => new Date(a.billDate).getTime() - new Date(b.billDate).getTime())
          .map(item => {
            const flowItem = finReceivedFlowRoList.find(flowItem => flowItem.orderNo === item.orderNo);
            console.log(finReceivedFlowRoList, flowItem);
            return {
              ...item,
              currReceivedAmount: flowItem?.receivedAmountYuan,
            };
          });

        setReceivableList(filterData);
        setEditableRowKeys(result?.map(item => item.orderNo));
        setSelectedRowKeys(finReceivedFlowRoList?.map(item => item.orderNo));
      });
    });
  }

  useEffect(() => {
    form.resetFields();
    setReceivableList([]);
    setEditableRowKeys([]);
    setSelectedRowKeys([]);
    if (serialNumber) {
      getData();
    }
  }, [])

  useEffect(() => {
    if (serialNumber) {
      getData();
    } else {
      form.resetFields();
      setReceivableList([]);
      setEditableRowKeys([]);
      setSelectedRowKeys([]);
    }
  }, [serialNumber]);


  const autoAssignOrders = () => {
    let totalReceivedAmount: number = form.getFieldValue("totalReceivedAmountYuan") || 0;
    let adjustAmount: number = form.getFieldValue("adjustAmount") || 0;
    let amountToAssign = _.add(totalReceivedAmount, adjustAmount);

    if (amountToAssign <= 0) {
      message.error(intl.formatMessage({ id: 'finance.receive.negativeAmountError' }));
      return;
    }

    const newSelectedKeys: React.Key[] = [];
    const displayedIds = new Set(receivableList.map(item => item.orderNo));

    const newMasterList = _receivableList.map(item => {
      const newItem = { ...item };
      // Only apply to items currently displayed
      if (displayedIds.has(newItem.orderNo)) {
        if (amountToAssign > 0) {
          const unreceived = newItem.remainReceivableAmountYuan || 0;
          if (unreceived > 0) {
            if (amountToAssign >= unreceived) {
              newItem.currReceivedAmount = unreceived;
              amountToAssign = _.round(_.subtract(amountToAssign, unreceived), 2);
              newSelectedKeys.push(newItem.orderNo);
            } else {
              newItem.currReceivedAmount = amountToAssign;
              amountToAssign = 0;
              newSelectedKeys.push(newItem.orderNo);
            }
          } else {
            newItem.currReceivedAmount = undefined;
          }
        } else {
          newItem.currReceivedAmount = undefined;
        }
      }
      return newItem;
    });

    setReceivableList(newMasterList);
    setSelectedRowKeys(newSelectedKeys);
  };

  const handleQueryReceivableList = async (params: any) => {
    const data = await queryReceivableList(params);
    const filteredData = data?.filter(item => item.remainReceivableAmountYuan != 0).sort((a, b) => new Date(a.billDate).getTime() - new Date(b.billDate).getTime());
    setReceivableList(filteredData);
    setEditableRowKeys(data?.map(item => item.orderNo));
  }

  useUpdateEffect(() => {
    setSelectedRowKeys([]);
    if (buyerId && !serialNumber) {
      queryTotalReceivable({ buyerId }).then(result => {
        setTotalReceivable(result ?? []);
      });
      handleQueryReceivableList({ buyerId, receivableFlag: 1, });
    } else {
      setTotalReceivable([]);
      setReceivableList([]);
    }
  }, [buyerId, buyerType]);

  const handleAccountSelectChange = async (id, option) => {
    setAccountSelect(option);
    form.setFieldValue('receivedAccountName', option?.label);
  };

  const back = () => {

  };

  const handleUpdate = (record: FinReceivableEntity) => {
    // Find the currency of the first selected item, if any
    let firstSelectedCurrency: string | undefined;
    if (selectedRowKeys.length > 0) {
      const firstSelectedItem = _receivableList.find(item => item.orderNo === selectedRowKeys[0]);
      if (firstSelectedItem) {
        firstSelectedCurrency = firstSelectedItem.currency;
      }
    }

    // If trying to add a new item by entering an amount
    if (record.currReceivedAmount) {
      // and its currency is different from already selected items
      if (firstSelectedCurrency && record.currency !== firstSelectedCurrency) {
        message.error(intl.formatMessage({ id: 'finance.receive.multiCurrencyError', defaultMessage: '每次收款只能核销一个币种的订单，不同币种订单请分开收款。' }));

        // Revert the change in the list
        const revertedList = _receivableList.map(item =>
          item.orderNo === record.orderNo ? { ...item, currReceivedAmount: undefined } : item
        );
        setReceivableList(revertedList);
        return; // Stop further processing
      }
    }

    const newReceivableList = _receivableList.map((item) =>
      item.orderNo === record.orderNo ? { ...item, currReceivedAmount: record.currReceivedAmount } : item
    );
    setReceivableList(newReceivableList);

    if (record.currReceivedAmount) {
      setSelectedRowKeys(keys => [...new Set([...keys, record.orderNo])]);
    } else {
      setSelectedRowKeys(keys => keys.filter(key => key !== record.orderNo));
    }
  };


  const orderSearch = (orderNo) => {
    setOrderNo(orderNo);
  };

  const handleAccountSelectRequest = async (query) => {
    const data = await queryMemberAccountPage({ memberAccountName: query.keyWords, pageSize: 1000 });
    const accountOptions = data?.data?.map(({ id, memberAccountName }) => ({
      value: id,
      label: memberAccountName,
    }));

    // 如果是首次加载数据，则设置默认值
    if (!accountSelect && data?.data && data.data.length > 0) {
      form.setFieldsValue({
        receivedAccountId: data.data[0].id,
        receivedAccountName: data.data[0].memberAccountName,
      });
      setAccountSelect(accountOptions?.[0]);
    }
    return accountOptions;
  };


  const saveFn = async () => {
    const formData = form.getFieldsValue();
    const selectedOrderDetailList = receivableList.filter(item => selectedRowKeys.includes(item.orderNo)).map(item => ({
      receivableId: item.id,
      receivedAmountYuan: item.currReceivedAmount,
      ledgerType: item.ledgerType
    }));
    if (!selectedOrderDetailList || selectedOrderDetailList.length == 0) {
      message.warning(intl.formatMessage({ id: 'finance.receive.noOrderSelectedWarning' }));
      return false;
    }

    return saveReceived({
      ...formData,
      buyerType: formData.buyerType === ObjectType.Customer ? 1 : 2,
      ledgerType: 1,
      finReceivedOrderDetailCmdList: selectedOrderDetailList,
      lossAmountYuan: localCurrencyLoss,
      writeOffAmount: `${currencySelect?.currencySymbol}${currTotalReceivedAmount.toFixed(2)}`,
      advanceAmountYuan: advanceAmount,
      serialNumber
    });
  }

  const onSave = async () => {
    const result = await saveFn();
    if (result) {
      message.success(intl.formatMessage({ id: 'common.message.submitSuccess' }));
      history.push('/finance/receive');
    }
  }

  const onSubmit = async () => {
    const serialNumber = await saveFn();
    if (serialNumber) {
      const updateReult = await updateReceivedStatus({
        serialNumber,
        status: ReceivedStatus.PENDING,
      })
      if (updateReult) {
        message.success(intl.formatMessage({ id: 'common.message.operationSuccess' }));
        history.push('/finance/receive');
      }
    }
  }

  return (
    <PageContainer>
      <ProForm
        submitter={false}
        form={form}
        layout='vertical'
        onFinish={onSave}
        initialValues={{
          currency: 'AUD',
          rate: 1
        }}
      >
        <ProCard className="rounded-lg">
          <ProForm.Group>
            <ProFormObject
              form={form}
              label={intl.formatMessage({ id: 'finance.receive.columns.customer' })}
              required
              rules={[REQUIRED_RULES]}
              objects={[ObjectType.Customer, ObjectType.OtherCompany]}
              fieldsName={{
                fieldType: 'buyerType',
                fieldName: 'buyerName',
                fieldId: 'buyerId',
              }}
              extra={Boolean(totalReceivable.length) && `${intl.formatMessage({ id: 'finance.receive.totalReceivable' })}: ${totalReceivable.map(item => `${item.currencySymbol}${item.amount.toFixed(2)}`).join(',')}`}
              disabled={serialNumber ? true : false}
            />
            <ProFormSelect
              width={width}
              required
              rules={[REQUIRED_RULES]}
              name="receiveStoreId"
              label={intl.formatMessage({ id: 'finance.receive.receiveStore' })}
              onChange={(value, option) => {
                form.setFieldsValue({
                  receiveStoreName: option.label,
                });
              }}
              request={async () => {
                const data = await queryStoreByAccount({});
                if (data && data.length > 0) {
                  form.setFieldsValue({
                    receiveStoreId: data[0].id,
                    receiveStoreName: data[0].name,
                  });
                }
                return data?.map(({ id, name }) => ({
                  value: id,
                  label: name,
                }));
              }}
            />
            <ProFormText hidden name="receiveStoreName" />
            <ProForm.Group size={0}>
              <ProFormSelect
                width={200}
                name="currency"
                label={intl.formatMessage({ id: 'finance.receive.receiveCurrency' })}
                request={
                  async (query) => {
                    const data = await queryAllCurrency({ currencyName: query.keyWords });
                    return data?.map(({ targetCurrency, rate, currencySymbol }) => ({
                      value: targetCurrency,
                      label: targetCurrency,
                      rate: rate,
                      currencySymbol,
                    }));
                  }
                }
                onChange={(value, option) => {
                  console.log(value, option);
                  setCurrencySelect({ ...option, targetCurrency: option.label });
                  form.setFieldsValue({ rate: option.rate, currencySymbol: option.currencySymbol });
                }}
              />
              <ProFormText name="currencySymbol" hidden />
              <ProFormDependency name={['currency']}>
                {({ currency }) => {
                  return <ProFormItem label=" " name='rate' >
                    <InputNumber addonBefore={intl.formatMessage({ id: 'finance.receive.rate' })} style={{ width: 125 }} disabled={currency == 'AUD'} onChange={(rate) => setCurrencySelect((preCurrency) => ({ ...preCurrency, rate }))} />
                  </ProFormItem>
                }}
              </ProFormDependency>
            </ProForm.Group>
            <ProFormMoney
              customSymbol=' ' // 空格不能去
              width={width}
              required
              name="totalReceivedAmountYuan"
              label={intl.formatMessage({ id: 'finance.receive.columns.receivedAmount' })}
              rules={[REQUIRED_RULES]}
              extra={<Button size="small" type="link" className='mx-0!' onClick={autoAssignOrders}>{intl.formatMessage({ id: 'finance.receive.autoAssign' })}</Button>}
            />
            <ProFormSelect
              width={width}
              required
              name="receivedAccountId"
              label={intl.formatMessage({ id: 'finance.receive.columns.receivedAccount' })}
              showSearch
              rules={[REQUIRED_RULES]}
              onChange={handleAccountSelectChange}
              request={handleAccountSelectRequest}
            />
            <ProFormText hidden name="receivedAccountName" />
            <ProFormSelect
              width={width}
              name="adjustType"
              label={intl.formatMessage({ id: 'finance.receive.adjustAmount' })}
              valueEnum={{
                [AdjustType.NONE]: intl.formatMessage({ id: 'finance.receive.adjustType.none' }),
                [AdjustType.ROUND]: intl.formatMessage({ id: 'finance.receive.adjustType.round' }),
                [AdjustType.DISCOUNT]: intl.formatMessage({ id: 'finance.receive.adjustType.discount' }),
              }}
              extra={
                <ProFormDependency name={['adjustType']}>
                  {({ adjustType }, form) => {
                    if (adjustType == AdjustType.DISCOUNT) {
                      return <div style={{ marginTop: 10, display: 'flex', justifyContent: 'space-between' }}>
                        <ProFormMoney
                          placeholder={intl.formatMessage({ id: 'finance.receive.adjustAmount' })}
                          customSymbol=' ' // 空格不能去
                          name="adjustAmount" width={160} />
                        <ProFormText name="adjustReason" width={160} placeholder={intl.formatMessage({ id: 'finance.receive.adjustReason' })} />
                      </div>
                    }
                    if (adjustType == AdjustType.ROUND) {
                      return <div style={{ marginTop: 10 }}>
                        <ProFormMoney
                          placeholder={intl.formatMessage({ id: 'finance.receive.adjustAmount' })}
                          customSymbol=' ' // 空格不能去
                          name="adjustAmount" width={160} />
                      </div>
                    }
                  }}
                </ProFormDependency>
              }
            />
          </ProForm.Group>
        </ProCard>
        <FunProTable<FinReceivableEntity, any>
          scroll={{ x: 'max-content' }}
          className="mt-4"
          rowKey="orderNo"
          title={() => (
            <Flex justify="space-between" align="flex-end">
              <LeftTitle title={intl.formatMessage({ id: 'finance.receive.writeOffOrder' })} />
              <Space>
                <Checkbox.Group value={overdueType} onChange={(e) => setOverdueType(e)}>
                  <Checkbox value={'Due'}>{intl.formatMessage({ id: 'finance.receive.filter.due' })}</Checkbox>
                  <Checkbox value={'Over Due'}>{intl.formatMessage({ id: 'finance.receive.filter.overdue' })}</Checkbox>
                </Checkbox.Group>
                <Search
                  placeholder={intl.formatMessage({ id: 'finance.receive.placeholders.businessOrderNo' })}
                  allowClear
                  onSearch={orderSearch}
                  style={{ width: 300 }}
                />
              </Space>
            </Flex>
          )}
          search={false}
          pagination={false}
          rowSelection={{
            selectedRowKeys,
            onChange: (keys, rows) => {
              if (rows.length > 1) {
                const firstCurrency = rows[0].currency;
                const allSameCurrency = rows.every(row => row.currency === firstCurrency);
                if (!allSameCurrency) {
                  message.error(intl.formatMessage({ id: '每次收款只能核销一个币种的订单，不同币种订单请分开收款。' }));
                  return;
                }
              }

              const newReceivableList = _receivableList.map(item => {
                if (keys.includes(item.orderNo)) {
                  // If newly selected, fill amount
                  if (!selectedRowKeys.includes(item.orderNo)) {
                    return { ...item, currReceivedAmount: item.remainReceivableAmountYuan };
                  }
                } else {
                  // If deselected, clear amount
                  if (selectedRowKeys.includes(item.orderNo)) {
                    return { ...item, currReceivedAmount: undefined };
                  }
                }
                return item;
              });
              setReceivableList(newReceivableList);
              setSelectedRowKeys(keys);
            },
          }}
          editable={{
            editableKeys
          }}
          dataSource={receivableList}
          options={false}
          columns={getCreateColumns(intl, { handleUpdate, currencySelect })}
        />
        <ProCard>
          <ProForm.Group colProps={{ span: 24 }}>
            <ProForm.Group>
              <ProFormUploadSingleCard
                name="receivePicFileList"
                label={intl.formatMessage({ id: 'finance.receive.receiptImage' })}
                onChange={(fileList) => {
                  console.log(fileList);
                  form?.setFieldsValue({
                    receivePicFileList: fileList,
                    receivePic: fileList?.[0]?.response?.data?.[0],
                  });
                }}
              />
              <ProFormText name="receivePic" hidden />
            </ProForm.Group>
            <ProForm.Group>
              <ProFormTextArea
                name="remark"
                label={intl.formatMessage({ id: 'finance.receive.columns.remark' })}
                width={600}
                fieldProps={{
                  count: { max: 100, show: true },
                  maxLength: 100,
                  style: { height: 102 }
                }}
              />
            </ProForm.Group>
          </ProForm.Group>
        </ProCard>

        <ProCard>
          <div className="flex justify-end gap-8 items-end" style={{ marginBottom: 40 }}>
            <div>
              <div className="flex flex-row items-center">
                <span className="text-[16px] font-semibold text-[#000000D9]">{intl.formatMessage({ id: 'finance.receive.summary.totalReceived' })}：</span>
                <span className="text-[24px] font-medium text-[#F83431]">{currencySelect?.currencySymbol}{watchedTotalReceivedAmountValue.toFixed(2)}</span>
              </div>
              {!isLocalCurrency && <div className="flex flex-row items-center">
                <span>{intl.formatMessage({ id: 'finance.receive.summary.local.totalReceived' })}：</span>
                <span>${localTotalReceivedAmountValue.toFixed(2)}</span>
              </div>}
            </div>
            <div>
              <div className="flex flex-row items-center">
                <span className="text-[16px] font-semibold text-[#000000D9]">{intl.formatMessage({ id: 'finance.receive.summary.totalAdjust' })}：</span>
                <span className="text-[24px] font-medium text-[#F83431]">{currencySelect?.currencySymbol}{watchedAdjustAmountValue.toFixed(2)}</span>
              </div>
              {
                !isLocalCurrency && <div className="flex flex-row items-center">
                  <span>{intl.formatMessage({ id: 'finance.receive.summary.local.totalAdjust' })}：</span>
                  <span>${localAdjustAmountValue.toFixed(2)}</span>
                </div>
              }
            </div>
            <div>
              <div className="flex flex-row items-center">
                <span className="text-[16px] font-semibold text-[#000000D9]">{intl.formatMessage({ id: 'finance.receive.summary.totalWriteOff' })}：</span>
                <span className="text-[24px] font-medium text-[#F83431]">{currencySelect?.currencySymbol}{currTotalReceivedAmount.toFixed(2)}</span>
              </div>
              {!isLocalCurrency && <div className="flex flex-row items-center">
                <span>{intl.formatMessage({ id: 'finance.receive.summary.local.totalWriteOff' })}：</span>
                <span>${localCurrTotalReceivedAmount.toFixed(2)}</span>
              </div>}
            </div>
            <div>
              <div className="flex flex-row items-center">
                <span className="text-[16px] font-semibold text-[#000000D9]">{intl.formatMessage({ id: 'finance.receive.summary.totalAdvance' })}：</span>
                <span className="text-[24px] font-medium text-[#F83431]">{currencySelect?.currencySymbol}{advanceAmount.toFixed(2)}</span>
              </div>
              {
                !isLocalCurrency && <div className="flex flex-row items-center">
                  <span>{intl.formatMessage({ id: 'finance.receive.summary.local.totalAdvance' })}：</span>
                  <span>${localAdvanceAmount.toFixed(2)}</span>
                </div>
              }
            </div>
            <div>
              <div className="flex flex-row items-center">

              </div>
              {!isLocalCurrency && <div className="flex flex-row items-center">
                <span>{intl.formatMessage({ id: 'finance.receive.summary.local.loss' })}：</span>
                <span>${localCurrencyLoss.toFixed(2)}</span>
              </div>}
            </div>
          </div>
          <Flex className="flex justify-end items-center w-full">
            <Space>
              {/* <Button key="rest" danger onClick={() => back()}>
                {intl.formatMessage({ id: 'common.button.cancel' })}
              </Button> */}
              <Button type="primary" ghost onClick={onSave}>
                {intl.formatMessage({ id: 'common.button.save' })}
              </Button>
              <Button type="primary" key="launch" onClick={onSubmit}>
                {intl.formatMessage({ id: 'finance.receive.confirm' })}
              </Button>
            </Space>
          </Flex>
        </ProCard>
      </ProForm>
    </PageContainer>
  );
};

export default withKeepAlive(AddReceived);