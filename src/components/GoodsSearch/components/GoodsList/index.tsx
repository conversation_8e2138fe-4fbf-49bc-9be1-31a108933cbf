import { goodsColumnsForExternalPurchase } from '@/components/GoodsSearch/components/GoodsList/config/goodsColumnsForExternalPurchase';
import { goodsColumnsForExternalPurchaseReturn } from '@/components/GoodsSearch/components/GoodsList/config/goodsColumnsForExternalPurchaseReturn';
import { goodsColumnsForSalesReturn } from '@/components/GoodsSearch/components/GoodsList/config/goodsColumnsForSalesReturn';
import type { Agg } from '@/components/GoodsSearch/components/GoodsList/types/Agg';
import type { StocksInfoDrawerProps } from '@/components/StocksInfoDrawer';
import StocksInfoDrawer from '@/components/StocksInfoDrawer';
import GoodsCreateDrawerForm from '@/pages/goods/list/components/GoodsCreateDrawerForm';
import type { GoodsCreateDrawerFormType } from '@/pages/goods/list/types/GoodsCreateDrawerFormType';
import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import ShortcutHelpDrawer from '@/pages/sales/order/edit/compoments/ShortcutHelp';
import { useModel } from '@@/exports';
import { PlusOutlined } from '@ant-design/icons';
import {
  ActionType,
  ColumnsState,
  EditableFormInstance,
  EditableProTable,
} from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { useDebounceEffect, useDebounceFn } from 'ahooks';
import { Button, Checkbox, ConfigProvider, message, Space } from 'antd';
import classNames from 'classnames';
import _ from 'lodash';
import React, { useCallback, useRef, useState } from 'react';
import { queryOrderGoodsForPurchase, queryStoreGoodsPage } from '../../services';
import { GoodsSearchBizType } from '../../types/BizType';
import { GoodsSearchTabType } from '../../types/TabType';
import { goodsColumnsForExternalPurchaseSalesSlip } from './config/goodsColumnsForExternalPurchaseSalesSlip';
import { goodsColumnsForPlatformPurchase } from './config/goodsColumnsForPlatformPurchase';
import { goodsColumnsForSalesSlip } from './config/goodsColumnsForSalesSlip';
import { useGoodsColumnsForStore } from './config/goodsColumnsForStore';
import styles from './index.module.scss';
import { QuerySalesSlipType } from './types/QuerySalesSlipType';
import type { EditGoodsEntity } from './types/edit.goods.entity';
import type { GoodsMultiEntity } from './types/goods.multi.entity';
import type { PurchaseGoodsEntity } from './types/purchase.goods.entity';
import type { QueryGoodsMultiRequest } from './types/query.goods.multi.request';
import type { QueryPurchaseGoodsPageRequest } from './types/query.purchase.goods.page.request';
import type { StoreGoodsEntity } from './types/store.goods.entity';
import GoodsDetailDrawer, { GoodsDetailDrawerProps } from '@/components/GoodsDetailDrawer';
import { ChannelCode } from '@/pages/goods/list/types/channel.code';
import CreateGoodSimpleModal from '@/components/CreateGoodSimpleModal';

export interface GoodsListProps {
  /**
   * 业务场景
   */
  bizType: GoodsSearchBizType;
  /**
   * 当前激活的tab
   */
  activeTabKey: GoodsSearchTabType;
  /**
   * 此所属组件的tab
   */
  tabKey: GoodsSearchTabType;
  /**
   * 表单数据
   */
  formData?: QueryGoodsMultiRequest;
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 仓库ID
   */
  warehouseId?: string;
  /**
   * 供应商ID
   */
  supplierId?: string;
  /**
   * 门店ID
   */
  storeId?: string;
  /**
   * 已选商品ID集合
   */
  addedItemSns?: string[];
  /**
   * 添加商品事件
   * @param itemList
   */
  onAdd: (itemList: any[]) => void;
  /**
   * VIN查询获取的额外品类品牌树信息
   * @param agg
   */
  setAgg?: (agg: Agg) => void;
}

const GoodsList = (props: GoodsListProps) => {
  const {
    bizType,
    setAgg,
    tabKey,
    activeTabKey,
    formData,
    warehouseId,
    supplierId,
    addedItemSns = [],
    onAdd,
    storeId,
    cstId,
  } = props;
  const intl = useIntl();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<EditableFormInstance>();
  // 查询条件集合
  const [params, setParams] = useState<QueryGoodsMultiRequest>();
  // 是否精确查找
  const [isAccurate, setIsAccurate] = useState(false);
  // 是否仅看有货
  const [hasStock, setHasStock] = useState(false);
  // 是否仅看供应商供应
  const [isSupplierSupply, setIsSupplierSupply] = useState(false);
  // 是否查看低于库存下限
  const [belowStocks, setBelowStocks] = useState(false);

  // 可编辑行
  const [editorRows, setEditorRows] = useState<string[]>([]);

  // 显示库存信息
  const [stocksDrawer, setStocksDrawer] = useState<StocksInfoDrawerProps>({
    visible: false,
  });

  // 查看商品
  const [goodsDrawer, setGoodsDrawer] = useState<GoodsDetailDrawerProps>({
    visible: false,
  });

  // 新增商品
  const [showAddGood, setShowAddGood] = useState(false);

  const [columnsCommonStateValue, setColumnsCommonStateValue] = useState<
    Record<string, ColumnsState>
  >({});

  const [shortcutHelpDrawerVisible, setShortcutHelpDrawerVisible] = useState<boolean>(false);

  // 新增商品
  const [createModalProps, setCreateModalProps] = useState<GoodsCreateDrawerFormType>({
    visible: false,
    recordId: 0,
    readOnly: false,
    title: `新增商品`,
    onCancel: () => null,
  });

  // 可选择行
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const [dataSourceCache, setDataSourceCache] = useState<any[]>([]);

  const { columnsStateValue, onColumnsStatChange } = useModel('saleModel');

  /**
   * 关闭【新增商品】对话框
   */
  const hideCreateModal = (reload: boolean) => {
    setCreateModalProps((preModalProps) => ({
      ...preModalProps,
      visible: false,
      recordId: 0,
      readOnly: false,
    }));
    if (!reload) return;
    const timer = setTimeout(() => {
      actionRef.current?.reload(reload);
      clearTimeout(timer);
    }, 1500);
  };

  // onAdd防抖
  const { run } = useDebounceFn(
    (data) => {
      onAdd([data]);
    },
    { wait: 300 },
  );

  // console.log('GoodsListProps', props);

  /**
   * 查询参数收集
   */
  useDebounceEffect(
    () => {
      // 只有当前显示的TAB是对应业务的TAB时，才响应搜索事件
      if (tabKey === activeTabKey) {
        const newFormData: QueryGoodsMultiRequest = {
          ...formData,
          isAccurate,
          cstId,
          storeId,
          warehouseId,
        };
        newFormData.itemStatus = YesNoStatus.YES;
        // 查询上次售价
        newFormData.isFetchLastSalePrice = true;
        // 只查询前置仓的商品
        newFormData.channelCode = ChannelCode.Store;
        if (warehouseId) {
          // 查询本地库存
          newFormData.isFetchLocalInventory = true;
          // 查看单仓成本价
          newFormData.isFetchWarehouseCostPrice = true;
          // 仅看有货
          newFormData.hasStock = hasStock;
          // 低于库存下限
          newFormData.belowStocks = belowStocks;
        }
        // 查询库位
        newFormData.isFetchLocation = true;
        // 如果是VIN查询，需要获取品牌聚合数据和类目聚合数据
        // if (tabKey === GoodsSearchTabType.VIN) {
        //   newFormData.isFetchBrandAggs = true;
        //   newFormData.isFetchCategoryAggs = true;
        // }

        if (tabKey === GoodsSearchTabType.SalesSlip) {
          if (bizType === GoodsSearchBizType.PlatformPurchase) {
            (newFormData as QueryPurchaseGoodsPageRequest).queryType =
              QuerySalesSlipType.PLATFORM_PURCHASE;
          }
          if (bizType === GoodsSearchBizType.ExternalPurchase) {
            (newFormData as QueryPurchaseGoodsPageRequest).queryType =
              QuerySalesSlipType.EXTERNAL_PURCHASE;
          }
        }
        if (bizType === GoodsSearchBizType.Sales) {
          // 查询分层定价
          newFormData.queryLevelPrice = true;
        }

        if (bizType === GoodsSearchBizType.ExternalPurchase && isSupplierSupply) {
          (newFormData as QueryPurchaseGoodsPageRequest).supplierId = supplierId;
        }

        setParams(newFormData);
      }
    },
    [formData, isAccurate, hasStock, isSupplierSupply, belowStocks, warehouseId, supplierId],
    { wait: 250 },
  );

  /**
   * 添加商品事件
   */
  const handleAdd = (itemList: any[]) => {
    const item = itemList[0];
    let formData: EditGoodsEntity = {};
    formData = formRef.current?.getRowData?.((item as StoreGoodsEntity).itemSn);

    if (tabKey === GoodsSearchTabType.SalesSlip) {
      formData = formRef.current?.getRowData?.(item.rowId);
    }

    if (
      !formData?.price &&
      [
        GoodsSearchBizType.Sales,
        GoodsSearchBizType.ExternalPurchase,
        GoodsSearchBizType.SalesReturn,
      ].includes(bizType)
    ) {
      message.warning(intl.formatMessage({ id: 'goods.search.message.fillPrice' }));
      return;
    }
    if (!formData?.number) {
      message.warning(intl.formatMessage({ id: 'goods.search.message.fillQuantity' }));
      return;
    }
    run({ ...item, ...formData });
    setSelectedRowKeys([]);
  };

  /**
   * 查看库存信息面板
   */
  const handleViewStocksInfo = useCallback(
    (data: StocksInfoDrawerProps) => {
      setStocksDrawer((drawerData) => ({
        ...drawerData,
        visible: !drawerData.visible,
        warehouseId,
        ...data,
      }));
    },
    [warehouseId],
  );

  /**
   * 根据不同场景选择不同columns
   */
  const getColumns = () => {
    switch (bizType) {
      case GoodsSearchBizType.Sales:
        return goodsColumnsForStore;
      case GoodsSearchBizType.PlatformPurchase:
        switch (tabKey) {
          case GoodsSearchTabType.SalesSlip:
            return goodsColumnsForSalesSlip({
              addedItemSns,
              handleAdd,
              handleViewStocksInfo,
            });
          default:
            return goodsColumnsForPlatformPurchase({
              addedItemSns,
              handleAdd,
            });
        }
      case GoodsSearchBizType.ExternalPurchase:
        switch (tabKey) {
          case GoodsSearchTabType.SalesSlip:
            return goodsColumnsForExternalPurchaseSalesSlip({
              addedItemSns,
              handleAdd,
              handleViewStocksInfo,
              intl,
            });
          default:
            return goodsColumnsForExternalPurchase({
              cstId,
              storeId,
              addedItemSns,
              handleAdd,
              handleViewStocksInfo,
              intl,
              setGoodsDrawer,
            });
        }
      case GoodsSearchBizType.ExternalPurchaseReturn:
        return goodsColumnsForExternalPurchaseReturn({
          addedItemSns,
          handleAdd,
          handleViewStocksInfo,
        });
      case GoodsSearchBizType.SalesReturn:
        return goodsColumnsForSalesReturn({ addedItemSns, handleAdd, handleViewStocksInfo, intl });
    }
  };

  /**
   * 根据不同场景选择不同请求方法
   */
  const getRequest = async (query: QueryGoodsMultiRequest) => {
    if (tabKey === GoodsSearchTabType.SalesSlip) {
      return await queryOrderGoodsForPurchase(query as QueryPurchaseGoodsPageRequest);
    }
    return await queryStoreGoodsPage(query);
  };

  /**
   * 根据不同场景选择不同的rowKey
   */
  const getRowKey = () => {
    if (tabKey === GoodsSearchTabType.SalesSlip) {
      return 'rowId';
    }
    return 'itemSn';
  };

  /**
   * 设置表格是否隐藏
   * 因为要调用表格实例，不能直接不渲染表格
   */
  const getTableVisible = () => {
    let visible = true;
    // 如果是VIN查询，且没有modelId，则不展示列表, 因为此场景modelId是表格查询的必填项
    // @ts-ignore
    if (tabKey === GoodsSearchTabType.VIN && !formData?.modelId) {
      visible = false;
    }
    return visible;
  };

  /**
   * 监听表格变化设置毛利数据
   */
  const handleValuesChange = (changedValues: any, allValues: any) => {
    if (allValues.costPrice) {
      formRef?.current?.setRowData?.(allValues.itemSn, {
        grossMargin: _.round(
          _.multiply(
            allValues.number ?? 0,
            _.subtract(allValues.price ?? 0, allValues.costPrice ?? 0),
          ),
          2,
        ),
      });
    }
    if (tabKey === GoodsSearchTabType.SalesSlip) {
      dataSourceCache.forEach((item) => {
        if (item.rowId === allValues.rowId) {
          item.price = allValues.price;
          item.number = allValues.number;
        }
      });
      setDataSourceCache([...dataSourceCache]);
    }
  };

  /**
   * 批量添加
   */
  const handleBatchAdd = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning(intl.formatMessage({ id: 'goods.search.message.selectGoods' }));
      return;
    }
    const selectDataList: any[] = dataSourceCache.filter((item) =>
      selectedRowKeys.includes(item.rowId),
    );
    const hasPriceItemSn = selectDataList.map((item) => (item.price ? item.itemSn : null));
    const hasNoPriceList = selectDataList.filter((obj) => !obj.price);
    const lastList = hasNoPriceList.filter((item1) => !hasPriceItemSn.includes(item1.itemSn));
    if (lastList.length > 0 && [GoodsSearchBizType.ExternalPurchase].includes(bizType)) {
      message.warning(intl.formatMessage({ id: 'goods.search.message.fillPrice' }));
      return;
    }
    const hasZeroValue = selectDataList.some((obj) => !obj.number || obj.number === 0);
    if (hasZeroValue) {
      message.warning(intl.formatMessage({ id: 'goods.search.message.fillQuantity' }));
      return;
    }
    await onAdd(selectDataList);
    setSelectedRowKeys([]);
  };

  console.log('bizType', bizType);

  const goodsColumnsForStore = useGoodsColumnsForStore({
    addedItemSns,
    handleAdd,
    handleViewStocksInfo,
    setGoodsDrawer,
    cstId,
    storeId,
    intl,
  });
  return (
    <div style={{ display: getTableVisible() ? 'block' : 'none' }}>
      <ConfigProvider
        theme={{
          components: {
            InputNumber: {
              controlWidth: 80,
            },
          },
        }}
      >
        <EditableProTable<GoodsMultiEntity, any>
          className={classNames(styles.goodsList, 'mb-[16px]', {
            'mt-[4px]': bizType === GoodsSearchBizType.Sales,
          })}
          onValuesChange={handleValuesChange}
          columnEmptyText={false}
          manualRequest={true}
          actionRef={actionRef}
          editableFormRef={formRef}
          pagination={{
            showQuickJumper: true,
            defaultPageSize: 10,
            showSizeChanger: true,
          }}
          options={{
            setting: true,
            density: false,
            reload: false,
          }}
          columnsState={
            bizType === GoodsSearchBizType.Sales
              ? {
                  value: columnsStateValue,
                  onChange: onColumnsStatChange,
                }
              : {
                  value: columnsCommonStateValue,
                  onChange: setColumnsCommonStateValue,
                }
          }
          params={params}
          // @ts-ignore
          request={async (query, sort) => {
            console.log('表格排序变化==', sort);
            // 排序方式，1-默认，2-近30天销量正序，3-近30天销量倒序，4-日均销量正序，5-日均销量倒序
            let sortType = 1;
            if (sort?.saleNum30d) {
              sortType = sort?.saleNum30d === 'ascend' ? 2 : 3;
            }
            if (sort?.saleAvgNum) {
              sortType = sort?.saleAvgNum === 'ascend' ? 4 : 5;
            }
            query.sortType = sortType;
            const result = await getRequest(query);

            if (result) {
              // 如果是VIN码查询，需要返回过滤后的品牌和品类树，用于表单筛选
              if (tabKey === GoodsSearchTabType.VIN) {
                // @ts-ignore
                setAgg(result.agg);
              }

              // 如果是销售单查询，需要拼接key值
              if (tabKey === GoodsSearchTabType.SalesSlip) {
                // 设置默认价格和数量
                result.data?.forEach((item) => {
                  item.price = (item as PurchaseGoodsEntity).purchasePrice;
                  item.number = (item as PurchaseGoodsEntity).saleNum;
                  item.rowId = `${(item as PurchaseGoodsEntity).orderId}${
                    (item as PurchaseGoodsEntity).id
                  }`;
                });
                if (bizType === GoodsSearchBizType.PlatformPurchase) {
                  setEditorRows(result.data?.map((item) => (item.etcNo ? item.rowId : '')));
                } else {
                  setEditorRows(result.data?.map((item) => item.rowId));
                }
                setSelectedRowKeys([]);
              } else {
                // 设置当前页全部行皆可编辑
                setEditorRows(result.data?.map((item) => (item as StoreGoodsEntity).itemSn));
                // 设置默认价格和数量
                result.data?.forEach((item) => {
                  let price;
                  let grossMargin;
                  switch (bizType) {
                    case GoodsSearchBizType.Sales:
                    case GoodsSearchBizType.SalesReturn:
                      price = (item as StoreGoodsEntity).suggestPrice;
                      if (price && (item as StoreGoodsEntity).costPrice) {
                        grossMargin = _.round(
                          _.multiply(
                            1,
                            _.subtract(price ?? 0, (item as StoreGoodsEntity).costPrice ?? 0),
                          ),
                          2,
                        );
                      }
                      break;
                    case GoodsSearchBizType.ExternalPurchase:
                    case GoodsSearchBizType.ExternalPurchaseReturn:
                      price = (item as StoreGoodsEntity).purchasePrice;
                      break;
                  }
                  formRef.current?.setRowData?.((item as StoreGoodsEntity).itemSn, {
                    price,
                    grossMargin,
                    number: 1,
                  });
                });
              }
              setDataSourceCache(result.data);
              return {
                data: result.data,
                total: result.total,
                success: true,
              };
            } else {
              return {
                data: [],
                total: 0,
                success: false,
              };
            }
          }}
          rowKey={getRowKey()}
          scroll={{ x: 800, y: 'max-content' }}
          ghost={true}
          recordCreatorProps={false}
          headerTitle={
            <Space>
              {[GoodsSearchBizType.Sales].includes(bizType) && (
                <Checkbox checked={isAccurate} onChange={() => setIsAccurate(!isAccurate)}>
                  {intl.formatMessage({ id: 'goods.search.checkbox.exactSearch' })}
                </Checkbox>
              )}
              {[GoodsSearchBizType.Sales].includes(bizType) && (
                <Checkbox
                  checked={hasStock}
                  disabled={!warehouseId}
                  onChange={() => setHasStock(!hasStock)}
                >
                  {intl.formatMessage({ id: 'goods.search.checkbox.inStockOnly' })}
                </Checkbox>
              )}

              {![GoodsSearchTabType.SalesSlip].includes(tabKey) &&
                [GoodsSearchBizType.PlatformPurchase, GoodsSearchBizType.ExternalPurchase].includes(
                  bizType,
                ) && (
                  <Checkbox
                    checked={belowStocks}
                    disabled={!warehouseId}
                    onChange={() => setBelowStocks(!belowStocks)}
                  >
                    {intl.formatMessage({ id: 'goods.search.checkbox.belowStockLimit' })}
                  </Checkbox>
                )}
              {tabKey === GoodsSearchTabType.SalesSlip && (
                <Button danger onClick={() => handleBatchAdd()}>
                  {intl.formatMessage({ id: 'goods.search.button.batchAdd' })}
                </Button>
              )}
              {[GoodsSearchBizType.Sales].includes(bizType) && (
                <Button type="text" onClick={() => setShowAddGood(true)}>
                  {intl.formatMessage({ id: 'goods.list.createGoodsTitle' })}
                  <PlusOutlined />
                </Button>
              )}
            </Space>
          }
          editable={{
            editableKeys: editorRows,
            actionRender: () => [],
          }}
          rowSelection={
            tabKey === GoodsSearchTabType.SalesSlip
              ? {
                  selectedRowKeys: selectedRowKeys,
                  preserveSelectedRowKeys: true,
                  onChange: (selectedKeys) => {
                    setSelectedRowKeys(selectedKeys);
                  },
                  getCheckboxProps: (record) => {
                    return {
                      disabled:
                        (bizType === GoodsSearchBizType.ExternalPurchase
                          ? props.addedItemSns?.includes(record.itemSn as string)
                          : props.addedItemSns?.includes(record.etcNo as string)) ||
                        (bizType === GoodsSearchBizType.PlatformPurchase && !record.etcNo),
                    };
                  },
                }
              : false
          }
          tableAlertRender={false}
          value={dataSourceCache}
          columns={getColumns()}
        />
      </ConfigProvider>
      <StocksInfoDrawer {...stocksDrawer} onClose={() => setStocksDrawer({})} />
      <GoodsCreateDrawerForm {...createModalProps} onCancel={hideCreateModal} />
      <ShortcutHelpDrawer
        open={shortcutHelpDrawerVisible}
        onOpenChange={setShortcutHelpDrawerVisible}
        onClose={() => setShortcutHelpDrawerVisible(false)}
      />
      <GoodsDetailDrawer
        {...goodsDrawer}
        onClose={() => setGoodsDrawer({ visible: false })}
        refresh={() => {
          setTimeout(() => {
            actionRef.current?.reload(true);
          }, 1000);
        }}
      />
      <CreateGoodSimpleModal
        onRefresh={() => {
          setTimeout(() => {
            actionRef.current?.reload(true);
          }, 1000);
        }}
        visible={showAddGood}
        onClose={() => setShowAddGood(false)}
      />
    </div>
  );
};

export default GoodsList;
