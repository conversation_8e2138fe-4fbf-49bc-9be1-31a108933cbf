import type { PageRequestParamsType } from '@/types/PageRequestParamsType';
import { Address, Contact, Tag } from './CustomerSaveEntity';

export interface CustomerEntity extends PageRequestParamsType {
  /**
      * 客户地址返回一个
      */
  addresses?: Address[];
  /**
   * 客户预收（多币种）
   */
  advanceAmountCurrency?: string;
  /**
   * 可用额度，单位：元
   */
  availableAmount?: number;
  /**
   * 联系人返回一个
   */
  contacts?: Contact[];
  /**
   * 客户创建时间
   */
  createTime?: string;
  /**
   * 是否挂账
   */
  credit?: boolean;
  /**
   * 信用账期
   */
  creditTerms?: number;
  /**
   * 客户ID
   */
  cstId?: string;
  /**
   * 客户名称
   */
  cstName?: string;
  /**
   * 客户编码
   */
  cstSn?: string;
  /**
   * 0=启用1=禁用
   */
  cstStatus?: number;
  /**
   * 客户状态名称
   */
  cstStatusName?: string;
  /**
   * 客户不收税，0为收税1为不收税
   */
  gstExcluded?: number;
  /**
   * 已开通商城，只要客户下有一联系人开通商城账号，则为是
   */
  hasMallPermission?: boolean;
  /**
   * 客户图片
   */
  images?: Image[];
  /**
   * 是否多币种1=多币种0=单币种
   */
  isMultiCurrency?: number;
  /**
   * 客户简称
   */
  nickName?: string;
  /**
   * 应收额度，单位：元
   */
  receivableAmount?: number;
  /**
   * 客户应收（多币种）
   */
  receivableAmountCurrency?: string;
  /**
   * 剩余账期
   */
  remainTerms?: number;
  /**
   * 客户备注
   */
  remark?: string;
  /**
   * 业务员ID
   */
  salesmanId?: string;
  /**
   * 业务员名
   */
  salesmanName?: string;
  /**
   * 结算类型CODWeeklyMonthly
   */
  settleType?: string;
  /**
   * 归属门店ID
   */
  storeId?: string;
  /**
   * 归属门店名称
   */
  storeName?: string;
  /**
   * 客户标签
   */
  tags?: Tag[];
  /**
   * 总额度，单位：元
   */
  totalAmount?: number;
  /**
   * 通用邮箱
   */
  universalEmail?: string;
  /**
   * 已用额度，单位：元
   */
  usedAmount?: number;
}
