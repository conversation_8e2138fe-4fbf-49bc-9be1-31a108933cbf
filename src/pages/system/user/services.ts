import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import { type PageRequestParamsType } from '@/types/PageRequestParamsType';
import { type PageResponseDataType } from '@/types/PageResponseDataType';
import type { ResponseDataType } from '@/types/ResponseDataType';
import { request } from '@/utils/request';
import { type PostEntity } from './list/types/post.entity';
import type { PostSelect } from './list/types/post.select';

/**
 * 列表查询
 * @param params
 * @returns
 */
export const queryPostList = async (params: Partial<PostEntity> & PageRequestParamsType) => {
  return request<PageResponseDataType<PostEntity>>(`/ipmspassport/AccountFacade/pageQuery`, {
    data: params,
  });
};

/**
 * 详情查询
 * @param params
 * @returns
 */
export const queryPostDetail = async (params: Partial<PostEntity>) => {
  return request<PostEntity>(`/ipmspassport/AccountFacade/detail`, {
    data: params,
  });
};
/**
 * 新增
 * @param params
 * @returns
 */
export const createPost = async (params: Partial<PostEntity>) => {
  return request<ResponseDataType<string>>(`/ipmspassport/AccountFacade/create`, {
    origin: true,
    data: params,
  });
};
/**
 * 编辑
 * @param params
 * @returns
 */
export const modifyPost = async (params: Partial<PostEntity>) => {
  return request<ResponseDataType<string>>(`/ipmspassport/AccountFacade/modify`, {
    origin: true,
    data: params,
  });
};

/**
 * 修改状态
 * @param params
 * @returns
 */
export const modifyStatusPost = async (params: Partial<PostEntity>) => {
  return request<string>(`/ipmspassport/AccountFacade/modifyStatus`, {
    data: params,
  });
};
/**
 * j检查状态
 * @param params
 * @returns
 */
export const checkEtcAccountPost = async (params: Partial<PostEntity>) => {
  return request<string>(`/ipmspassport/AccountFacade/checkEtcAccount`, {
    data: params,
  });
};
/**
 * 查询一体系账号对应的门店信息
 * @param params
 * @returns
 */
export const queryEtcAccountPost = async (params: Partial<PostEntity>) => {
  return request<PostEntity>(`/ipmspassport/AccountFacade/queryEtcAccount`, {
    data: params,
  });
};

/**
 * 查询门店
 * @returns
 */
export const allSimpleQuery = async ({ status = YesNoStatus.YES }: { status?: YesNoStatus }) => {
  return request<PostSelect[]>(`/ipmspassport/StoreFacade/listQuerySimple`, { data: { status } });
};

export const allStoreSimpleQuery = async (params: { status?: YesNoStatus }) => {
  return request<PostSelect[]>(`/ipmspassport/StoreFacade/listQuerySimple`, { data: params });
};

/**
 * 查询门店关联的账户列表
 * @returns
 */
export const queryAccountSelectByStoreId = async (params: { storeId?: string }) => {
  return request<PostSelect[]>(`/ipmspassport/AccountFacade/queryStoreAccount`, { data: params });
};
/**
 * 查询当前登录用户所有权限门店
 * @returns
 */
export const queryStoreByAccount = async (params?: { accountId?: string; status?: 0 | 1 }): Promise<PostSelect[]> => {
  return request(`/ipmspassport/AccountFacade/queryStore`, { data: params ?? {} });
};

/**
 * 查询账户/业务员/员工列表
 * @returns
 */
export const accountListQuerySimple = async ({
  status = YesNoStatus.YES,
  name = '',
}: {
  status?: YesNoStatus;
  name?: string;
}): Promise<PostSelect[]> => {
  return request(`/ipmspassport/AccountFacade/listQuerySimple`, {
    data: { status, name },
  });
};
