import { getCstList } from '@/pages/customer/list/services';
import { YesNoStatus } from '@/pages/purchase/supplier/operation/types/YesNo';
import { updateOrderMain } from '@/pages/sales/order/edit/services';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { queryStoreByAccount } from '@/pages/system/user/services';
import { REQUIRED_RULES } from '@/utils/RuleUtils';
import { ProForm, ProFormInstance } from '@ant-design/pro-components';
import { ProFormSelect } from '@ant-design/pro-components';
import { ProFormText } from '@ant-design/pro-form';
import { useIntl } from '@umijs/max';
import type { MutableRefObject } from 'react';
import { useRef } from 'react';
import ProFormCurrency from '@/components/ProFormItem/ProFormCurrency';

export interface CstFormProps {
  cstFormRef: MutableRefObject<ProFormInstance>;
  orderId?: string;
  orderNo?: string;
  setWarehouseId: (id: string) => void;
  setStoreId: (id: string) => void;
  isMultiCurrency?: boolean;
}

const CstForm = (props: CstFormProps) => {
  const intl = useIntl();
  const {
    cstFormRef,
    orderId,
    orderNo,
    setWarehouseId,
    setStoreId,
    isMultiCurrency = false,
  } = props;
  // 这里的状态仅仅为了缓存取name值
  const cstListRef = useRef<any>();
  const storeListRef = useRef<any>();
  const warehouseListRef = useRef<any>();
  return (
    <ProForm.Group>
      <ProFormSelect
        width={250}
        debounceTime={300}
        label={intl.formatMessage({ id: 'sales.order.edit.customerName' })}
        placeholder={intl.formatMessage({ id: 'sales.order.edit.selectCustomer' })}
        name="cstId"
        allowClear={false}
        showSearch={true}
        rules={[REQUIRED_RULES]}
        fieldProps={{
          filterOption: false,
        }}
        disabled={Boolean(orderNo)}
        onChange={(value, item) => {
          cstFormRef?.current?.setFieldValue?.('cstName', item.title);
        }}
        request={(query) =>
          getCstList({ keyword: query.keyWords, cstStatus: 0 }).then((result) => {
            const list = result.map((item) => ({ label: item.cstName, value: item.cstId }));
            cstListRef.current = list;
            return list;
          })
        }
        colProps={{
          span: 6,
        }}
      />
      <ProFormText hidden name="cstName" />
      <ProFormSelect
        width={250}
        label={intl.formatMessage({ id: 'sales.order.edit.salesStore' })}
        placeholder={intl.formatMessage({ id: 'sales.order.edit.selectSalesStore' })}
        name="storeId"
        rules={[REQUIRED_RULES]}
        allowClear={false}
        disabled={Boolean(orderNo)}
        onChange={(value, item) => cstFormRef?.current?.setFieldsValue?.({ storeName: item.label })}
        request={(query) =>
          queryStoreByAccount({ status: 1 }).then((result) => {
            const list = result?.map((item) => ({ label: item.name, value: item.id })) ?? [];
            storeListRef.current = list;
            // 查看是否有默认值
            if (!orderNo) {
              let defaultItem;
              defaultItem = result?.find((item) => item.type === '0');
              if (!defaultItem) {
                defaultItem = result?.[0];
              }
              if (defaultItem) {
                cstFormRef?.current?.setFieldsValue?.({
                  storeId: defaultItem.id,
                  storeName: defaultItem.name,
                });
                setStoreId(defaultItem.id);
              }
            }
            return list;
          })
        }
        colProps={{
          span: 6,
        }}
      />
      <ProFormText hidden name="storeName" />
      <ProFormSelect
        width={250}
        label={intl.formatMessage({ id: 'sales.order.edit.deliveryWarehouse' })}
        placeholder={intl.formatMessage({ id: 'sales.order.edit.selectDeliveryWarehouse' })}
        name="warehouseId"
        rules={[REQUIRED_RULES]}
        allowClear={false}
        dependencies={['storeId']}
        onChange={(value, item) => {
          cstFormRef?.current?.setFieldsValue?.({ warehouseName: item.label });
          if (orderNo) {
            updateOrderMain({
              orderId: orderId,
              warehouseName: item.label,
              warehouseId: item.value,
            });
          }
        }}
        request={(query) =>
          warehouseList({
            state: YesNoStatus.YES,
            storeIdList: [query.storeId],
          }).then((result) => {
            const list =
              result?.warehouseStoreRelationRoList?.map((item) => ({
                label: item.warehouseName,
                value: item.warehouseId,
              })) ?? [];
            warehouseListRef.current = list;
            // 查看是否有默认值
            if (!orderNo) {
              let defaultItem;
              defaultItem = result?.warehouseStoreRelationRoList?.find((item) => item.isDefault);
              if (!defaultItem) {
                defaultItem = result?.warehouseStoreRelationRoList?.[0];
              }
              if (defaultItem) {
                cstFormRef?.current?.setFieldsValue?.({
                  warehouseId: defaultItem.warehouseId,
                  warehouseName: defaultItem.warehouseName,
                });
                setWarehouseId(defaultItem.warehouseId!);
              }
            }
            return list;
          })
        }
        colProps={{
          span: 6,
        }}
      />
      {isMultiCurrency && (
        <ProFormCurrency
          disabled={Boolean(orderNo)}
          colProps={{
            span: 6,
          }}
        />
      )}
      <ProFormText hidden name="warehouseName" />
    </ProForm.Group>
  );
};

export default CstForm;
