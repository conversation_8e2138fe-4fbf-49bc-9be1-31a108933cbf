
import FunProTable from '@/components/common/FunProTable';
import withKeep<PERSON>live from '@/wrappers/withKeepAlive';
import { PageContainer } from '@ant-design/pro-components';
import type { ActionType } from '@ant-design/pro-table';
import { useIntl } from '@umijs/max';
import { Button, message, Space } from 'antd';
import { useRef, useState } from 'react';
import { cancelDelivery, finishDelivery, queryDeliveryPage, startDelivery } from '../services';
import { DeliveryEntity } from '../types/Delivery.entity';
import BatchAssignModal from './components/BatchAssignModal';
import CreateTaskModal from './components/CreateTaskModal';
import { useDeliveryListColumns } from './config/deliveryListColumns';
import type { DeliveryListItem, DeliveryListSearchParams } from './type';

const Page = () => {
    const intl = useIntl();
    const t = (id: string, ...rest: any[]) => intl.formatMessage({ id }, ...rest);
    const actionRef = useRef<ActionType>();

    const [selectedRows, setSelectedRows] = useState<DeliveryListItem[]>([]);
    const [batchAssignModalVisible, setBatchAssignModalVisible] = useState(false);
    const [createTaskModalVisible, setCreateTaskModalVisible] = useState(false);
    const [editId, setEditId] = useState<string | undefined>(undefined);

    const handleStartDelivery = async (record: DeliveryListItem) => {
        await startDelivery({ id: record.id });
        message.success(t('stocks.delivery.list.message.startDeliverySuccess'));
        actionRef.current?.reload();
    };

    const handleFinishDelivery = async (record: DeliveryListItem) => {
        await finishDelivery({ id: record.id });
        message.success(t('stocks.delivery.list.message.finishDeliverySuccess'));
        actionRef.current?.reload();
    };

    const handleAssign = (record: DeliveryListItem) => {
        // For single assignment, we can reuse the batch assign modal
        setSelectedRows([record]);
        setBatchAssignModalVisible(true);
    };

    const handleEdit = (record: DeliveryListItem) => {
        setEditId(record.id);
        setCreateTaskModalVisible(true);
    };

    const handleCancelDelivery = async (record: DeliveryEntity) => {
        const result = await cancelDelivery({ id: record.id });
        if (result) {
            message.success(t('common.message.operation.success'));
            actionRef.current?.reload();
        }
    };

    const columns = useDeliveryListColumns({
        handleAssign,
        handleStartDelivery,
        handleFinishDelivery,
        handleEdit,
        handleCancelDelivery
    });


    return (
        <PageContainer>
            <FunProTable<DeliveryListItem, DeliveryListSearchParams>
                rowKey="id"
                actionRef={actionRef}
                requestPage={queryDeliveryPage}
                scroll={{ x: 'max-content' }}
                columns={columns}
                rowSelection={{
                    onChange: (_, rows) => {
                        setSelectedRows(rows);
                    },
                }}
                headerTitle={<Space>
                    <Button
                        type="primary"
                        key="createTask"
                        onClick={() => setCreateTaskModalVisible(true)}
                    >
                        {t('stocks.delivery.list.button.createTask')}
                    </Button>
                    <Button
                        type="primary"
                        key="batchAssign"
                        disabled={selectedRows.length === 0}
                        onClick={() => setBatchAssignModalVisible(true)}
                    >
                        {t('stocks.delivery.list.button.batchAssign')}
                    </Button>
                </Space>
                }
            />
            <BatchAssignModal
                visible={batchAssignModalVisible}
                onClose={() => setBatchAssignModalVisible(false)}
                selectedRows={selectedRows}
                onSuccess={() => {
                    actionRef.current?.reload()
                    setSelectedRows([])
                }}
            />
            <CreateTaskModal
                visible={createTaskModalVisible}
                onClose={() => {
                    setCreateTaskModalVisible(false);
                    setEditId(undefined);
                }}
                onSuccess={() => actionRef.current?.reload()}
                editId={editId}
            />
        </PageContainer>
    );
};

export default withKeepAlive(Page);
