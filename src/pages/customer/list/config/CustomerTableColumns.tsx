import { queryStoreByAccount } from '@/pages/system/user/services';
import { ReverseCommonStatusValueEnum } from '@/types/CommonStatus';
import type { ProColumns } from '@ant-design/pro-components';
import { isEmpty } from 'lodash';
import { getTagList } from '../services';
import type { CustomerEntity } from '../types/CustomerEntity';
import type { CustomerTagEntity } from '../types/CustomerTagEntity';

export const CustomerTableColumns = (intl: any): ProColumns<CustomerEntity>[] => [
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.search.customerInfo' }),
    dataIndex: 'cstWord',
    ellipsis: true,
    hideInTable: true,
    fieldProps: {
      placeholder: intl.formatMessage({ id: 'customer.customerList.table.search.customerInfo.placeholder' }),
    },
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.nickName' }),
    dataIndex: 'nickName',
    search: false,
    width: 100,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.ABN' }),
    dataIndex: 'abn',
    hideInSearch: true,
    width: 100,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.storeName' }),
    dataIndex: 'storeName',
    width: 120,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.salesmanName' }),
    dataIndex: 'salesmanName',
    width: 80,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.customerTags' }),
    dataIndex: 'tags',
    hideInSearch: true,
    width: 100,
    ellipsis: true,
    renderText: (text: CustomerTagEntity[]) => {
      return isEmpty(text) ? '' : text.map((t) => t.tagName).join(',');
    },
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.status' }),
    dataIndex: 'cstStatus',
    width: 80,
    valueEnum: ReverseCommonStatusValueEnum,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.settleType' }),
    dataIndex: 'settleType',
    width: 80,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.advanceAmount' }),
    dataIndex: 'advanceAmountCurrency',
    width: 120,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.receivableAmount' }),
    dataIndex: 'receivableAmountCurrency',
    width: 120,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.universalEmail' }),
    dataIndex: 'universalEmail',
    width: 120,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.search.contactName' }),
    dataIndex: 'contact',
    width: 120,
    renderText: (text, record) => {
      return <>{record?.contacts?.[0]?.firstName} {record?.contacts?.[0]?.lastName}</>;
    },
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.defaultContactPhone' }),
    dataIndex: ['contacts', 0, 'phone'],
    width: 100,
    hideInSearch: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.search.contactPhone' }),
    dataIndex: 'phone',
    hideInTable: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.Suburb' }),
    dataIndex: ['addresses', 0, 'provinceCode'],
    width: 100,
    hideInSearch: true,
    ellipsis: true,
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.column.defaultAddress' }),
    dataIndex: 'fullAddress',
    width: 180,
    hideInSearch: true,
    ellipsis: true,
    renderText: (text, record) => {
      return <>{record?.addresses?.[0]?.fullAddress}</>;
    },
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.search.customerTags' }),
    dataIndex: 'tagId',
    width: 120,
    hideInTable: true,
    valueType: 'select',
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 3,
    },
    search: {
      transform: (value) => ({
        tagIds: value,
      }),
    },
    request: async () => {
      const tagList = await getTagList({ tagStatus: 0, tagType: 1 });
      return tagList?.map((t: any) => ({ label: t.tagName, value: t.id })) || [];
    },
  },

  {
    title: intl.formatMessage({ id: 'customer.customerList.table.search.store' }),
    dataIndex: 'storeIds',
    hideInTable: true,
    valueType: 'select',
    fieldProps: {
      mode: 'multiple',
      showSearch: true,
      maxTagCount: 3,
      fieldNames: { label: 'name', value: 'id' },
    },
    request: () => queryStoreByAccount({ status: 1 }),
  },
  {
    title: intl.formatMessage({ id: 'customer.customerList.table.search.createTime' }),
    dataIndex: 'createTime',
    valueType: 'dateRange',
    hideInTable: true,
    search: {
      transform: ([beginTime, endTime]) => ({ beginTime, endTime }),
    },
    // "fieldProps": {
    //   "showTime": {
    //     "defaultValue": [
    //       dayjs("00:00:00", 'HH:mm:ss'),
    //       dayjs("23:59:59", 'HH:mm:ss'),
    //     ]
    //   }
    // }
  },
];
