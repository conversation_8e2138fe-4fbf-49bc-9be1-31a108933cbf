import AuthButton from '@/components/common/AuthButton';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { accountListQuerySimple, queryStoreByAccount } from '@/pages/system/user/services';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { Link } from '@umijs/max';
import { Popconfirm, Space, Tag, Tooltip } from 'antd';
import { isEmpty } from 'lodash';
import type { MutableRefObject } from 'react';
import type { IntlShape } from 'react-intl';
import { SupplierIdColumn, TimeAt } from '../../../common/config/tableColumns';
import { balanceStatusOptions } from '../types/BalanceStatus';
import { OrderSourceStatus } from '../types/OrderSourceStatus';
import { OrderStatus, orderStatusOptions } from '../types/OrderStatus';
import { payStatusOptions } from '../types/PayStatus';
import { payTypeStatusOptions } from '../types/PayTypeStatus';
import type { PurchasePostEntity } from '../types/purchase.post.entity';

export interface PostListTableColumnsProps {
  handleAgainItem: (orderNo: string) => void;
  handleUpdateItem: (orderId: string) => void;
  handleClosePurchase: (orderNo: string) => void;
  handleWithdraw: (orderNo: string) => void;
  formRef: MutableRefObject<ProFormInstance | undefined>;
  intl: IntlShape;
}

export const PostListTableColumns = (props: PostListTableColumnsProps) =>
  [
    {
      title: props.intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      fixed: 'left',
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.orderNo' }),
      dataIndex: 'orderNo',
      key: 'orderNo',
      fixed: 'left',
      order: 10,
      search: true,
      width: 100,
      render: (_, record) => (
        <Space>
          <Link
            to={{
              pathname: '/purchase/detail',
              search: '?purchaseId=' + record.id + '&purchaseOrderNo=' + record.orderNo,
            }}
          >
            {_}
          </Link>
          {record.orderSource == OrderSourceStatus.BHD_PURCHASE && (
            <Tooltip placement="top" title={record.sourceNo}>
              <Tag color="green">{props.intl.formatMessage({ id: 'purchase.list.tag.replenishment' })}</Tag>
            </Tooltip>
          )}
        </Space>
      ),
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.orderTime' }),
      dataIndex: 'orderTime',
      key: 'orderTime',
      search: false,
      width: 180,
    },
    TimeAt(props.intl.formatMessage({ id: 'purchase.list.columns.orderTime' })),
    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.supplierName' }),
      dataIndex: 'supplierName',
      key: 'supplierName',
      search: false,
      order: 6,
      ellipsis: true,
      width: 120,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.sourceNo' }),
      dataIndex: 'sourceNo',
      key: 'sourceNo',
      search: true,
      ellipsis: true,
      width: 140,
      order: 10,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.goodsInfo' }),
      dataIndex: 'sku',
      key: 'sku',
      order: 5,
      fieldProps: { placeholder: props.intl.formatMessage({ id: 'purchase.list.columns.goodsInfo.placeholder' }) },
      search: true,
      hideInTable: true,
    },
    { ...SupplierIdColumn },
    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.purchaseStore' }),
      dataIndex: 'storeName',
      key: 'storeName',
      search: false,
      width: 120,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.purchaseStore' }),
      dataIndex: 'storeId',
      key: 'storeId',
      search: true,
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        onChange: (e) => {
          props.formRef?.current?.setFieldValue('receiveWarehouseIdList', []);
        },
      },
      request: async () => {
        const data = await queryStoreByAccount();
        return data?.map(({ id, name }) => ({
          key: id,
          value: id,
          label: name,
        }));
      },
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.receiveWarehouse' }),
      dataIndex: 'receiveWarehouseName',
      key: 'receiveWarehouseName',
      debounceTime: 300,
      valueType: 'select',
      width: 120,
      formItemProps: {
        name: 'receiveWarehouseIdList',
      },
      fieldProps: {
        mode: 'multiple',
        maxTagCount: 3,
        fieldNames: { label: 'warehouseName', value: 'id' },
      },
      dependencies: ['storeId'],
      request: (query: any) => {
        if (!isEmpty(query.storeId)) {
          return warehouseList({ storeIdList: [query.storeId] }).then((s: any) => {
            return s.warehouseSimpleRoList ?? [];
          });
        } else {
          return new Promise((resolve, reject) => {
            return reject([]);
          });
        }
      },
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.currency' }),
      dataIndex: 'currency',
      key: 'currency',
      search: false,
      width: 80,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.purchaseAmount' }),
      dataIndex: 'shouldTotalAmount',
      key: 'shouldTotalAmount',
      width: 80,
      search: false,
      ellipsis: true,
    },

    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.payType' }),
      dataIndex: 'payType',
      key: 'payType',
      search: false,
      width: 80,
      valueEnum: payTypeStatusOptions,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.payStatus' }),
      dataIndex: 'payStatus',
      key: 'payStatus',
      order: 8,
      search: true,
      width: 80,
      valueEnum: payStatusOptions,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.balanceStatus' }),
      dataIndex: 'balanceStatus',
      key: 'balanceStatus',
      search: true,
      order: 7,
      width: 80,
      valueEnum: balanceStatusOptions,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.orderStatus' }),
      dataIndex: 'orderStatus',
      key: 'orderStatus',
      search: true,
      order: 9,
      width: 80,
      valueEnum: orderStatusOptions,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.purchaseUser' }),
      dataIndex: 'purchaseUser',
      key: 'purchaseUser',
      search: false,
      width: 60,
    },
    {
      title: props.intl.formatMessage({ id: 'purchase.list.columns.purchaseUser' }),
      dataIndex: 'purchaseUserId',
      key: 'purchaseUserId',
      search: true,
      width: 100,
      valueType: 'select',
      hideInTable: true,
      fieldProps: {
        showSearch: true,
        fieldNames: { label: 'name', value: 'id' },
      },
      request: (query: any) => {
        return accountListQuerySimple({ name: query.keyWords });
      },
    },
    {
      title: props.intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 80,
      fixed: 'right',
      render: (text, record: PurchasePostEntity) => {
        return (
          <Space>
            {(OrderStatus.DRAFT == record.orderStatus ||
              OrderStatus.REJECT == record.orderStatus) && [OrderSourceStatus.NORMAL_PURCHASE, OrderSourceStatus.BHD_PURCHASE].includes(record.orderSource!)
              && (
                <>
                  <AuthButton
                    isHref
                    authority="editPurchase"
                    onClick={() => props.handleUpdateItem(record.id ?? '')}
                  >
                    {props.intl.formatMessage({ id: 'purchase.list.button.edit' })}
                  </AuthButton>
                  <Popconfirm
                    title={props.intl.formatMessage({ id: 'purchase.list.confirm.void' })}
                    onConfirm={() => props.handleClosePurchase(record.orderNo ?? '')}
                  >
                    <AuthButton isHref authority="deletePurchase">
                      {props.intl.formatMessage({ id: 'purchase.list.button.void' })}
                    </AuthButton>
                  </Popconfirm>
                </>
              )}
            {OrderStatus.AUDITING == record.orderStatus &&
              [OrderSourceStatus.NORMAL_PURCHASE, OrderSourceStatus.BHD_PURCHASE].includes(record.orderSource!) && (
                <Popconfirm
                  title={props.intl.formatMessage({ id: 'purchase.list.confirm.void' })}
                  onConfirm={() => props.handleClosePurchase(record.orderNo ?? '')}
                >
                  <AuthButton isHref authority="deletePurchase">
                    {props.intl.formatMessage({ id: 'purchase.list.button.void' })}
                  </AuthButton>
                </Popconfirm>
              )}
            {[OrderSourceStatus.NORMAL_PURCHASE, OrderSourceStatus.BHD_PURCHASE].includes(record.orderSource!) &&
              OrderStatus.TO_ARRIVAL == record.orderStatus && (
                <Space>
                  <Popconfirm
                    title={props.intl.formatMessage({ id: 'purchase.list.confirm.void' })}
                    onConfirm={() => props.handleClosePurchase(record.orderNo ?? '')}
                  >
                    <AuthButton isHref authority="deletePurchase">
                      {props.intl.formatMessage({ id: 'purchase.list.button.void' })}
                    </AuthButton>
                  </Popconfirm>
                  <Popconfirm
                    title={props.intl.formatMessage({ id: 'purchase.list.confirm.withdraw' })}
                    onConfirm={() => props.handleWithdraw(record.orderNo ?? '')}
                  >
                    <AuthButton isHref authority="withdrawPurchase">
                      {props.intl.formatMessage({ id: 'purchase.list.button.withdraw' })}
                    </AuthButton>
                  </Popconfirm>
                </Space>
              )}
            {record.orderSource == OrderSourceStatus.ETC_PURCHASE && (
              <AuthButton
                isHref
                authority="purchaseAgain"
                onClick={() => props.handleAgainItem(record.orderNo ?? '')}
              >
                {props.intl.formatMessage({ id: 'purchase.list.button.againOrder' })}
              </AuthButton>
            )}
          </Space>
        );
      },
    },
  ] as ProColumns<PurchasePostEntity>[];
