export default {
    'location.text.location': 'Location',
    'location.text.batchImportLocation': 'Batch Import Location',
    'location.title.editLocation': 'Edit Location',
    'location.title.addLocation': 'Add Location',
    'location.button.printTag': 'Print Tag',
    'location.label.locationName': 'Location Code',
    'location.label.warehouseName': 'Affiliated Warehouse',
    'location.label.locationRemark': 'Location Remark',
    'location.label.locationInfo': 'Location Information',
    'location.label.area': 'Area',
    'location.label.shelf': 'Shelf',
    'location.label.layer': 'Layer',
    'location.label.position': 'Position',
    'location.label.enable': 'Enable',
    'warehouse.button.editWarehouse': 'Edit warehouse',
    'warehouse.button.addWarehouse': 'Add warehouse',
    'warehouse.label.warehouseName': 'Warehouse Name',
    'warehouse.label.affiliatedStore': 'Affiliated Store',
    'warehouse.tooltip.disabledStore': 'Red stores are expired.',
    'warehouse.message.defaultStoreDeletionNotAllowed': 'Default store cannot be deleted!',
    'warehouse.confirm.enableDisable': 'Confirm {action}?',
    'check.list.title.checkList': 'Inventory List',
    'check.list.saveOrUpdate': 'Save or Update',
    'check.list.label.bizBillNo': 'Business Bill Number',
    'check.list.label.warehouseName': 'Warehouse Name',
    'check.list.label.createTime': 'Creation Time',
    'check.list.label.state': 'Check State',
    'check.list.label.goodsInfo': 'Goods Information',
    'check.list.label.createDocPerson': 'Creator',
    'check.list.label.createDocTime': 'Creation Time',
    'check.list.label.profitNum': 'Profit Quantity',
    'check.list.label.lossNum': 'Loss Quantity',
    'check.list.label.profitAmount': 'Profit Amount',
    'check.list.label.lossAmount': 'Loss Amount',
    'check.list.placeholder.goodsNameOrCode': 'Goods Name or Code',
    'check.list.button.continueCheck': 'Continue Check',
    'check.list.button.addCheck': 'Add Check',
    'check.detail.confirm.void': 'Are you sure to void this? ',
    'check.detail.button.void': 'Void',
    'check.detail.button.continueCheck': 'Continue Check',
    'check.detail.button.audit': 'Audit',
    'check.detail.label.remarks': 'Remarks',
    'check.detail.label.checkMode': 'Check Mode',
    'check.detail.label.itemSn': 'Item Code',
    'check.detail.label.itemName': 'Item Name',
    'check.detail.label.oeNo': 'OE No.',
    'check.detail.label.brandPartNo': 'Brand Part No.',
    'check.detail.label.brandName': 'Brand',
    'check.detail.label.categoryName': 'Category',
    'check.detail.label.unitName': 'Unit',
    'check.detail.label.stockAmount': 'Stock Amount',
    'check.detail.label.checkAmount': 'Check Amount',
    'check.detail.label.diffAmount': 'Diff Amount',
    'check.detail.label.auditPerson': 'Auditor',
    'check.detail.label.auditTime': 'Audit Time',
    'check.detail.label.rejectReason': 'Reject Reason',
    'check.detail.title.checkGoods': 'Check Goods',
    'output.list.title': 'Output Order List',
    'output.list.button.output': 'Output',
    'output.list.button.outputRecord': 'Output Record',
    'output.list.label.businessOrderNo': 'Business Order No.',
    'output.list.label.outputOrderNo': 'Output Order No.',
    'output.list.label.goodsInfo': 'Goods Information',
    'output.list.label.outputType': 'Output Type',
    'output.list.label.outputStatus': 'Output Status',
    'output.list.label.notifyOutputTime': 'Notify Output Time',
    'output.list.label.outputCompleteTime': 'Output Complete Time',
    'output.list.label.outputWarehouse': 'Output Warehouse',
    'output.list.label.outputQuantity': 'Output Quantity',
    'output.list.label.recipient': 'Recipient',
    'output.list.label.deliveryMethod': 'Delivery Method',
    'output.list.label.deliveryAddress': 'Delivery Address',
    'output.list.label.logisticsCompany': 'Logistics Company',
    'output.list.label.logisticsNo': 'Logistics No.',
    'output.list.placeholder.goodsNameOrCode': 'Goods Name/Code',
    'output.modal.title.output': 'Output',
    'output.modal.title.outputRecord': 'Output Record',
    'output.modal.button.confirmOutput': 'Confirm Output',
    'output.modal.subtitle.goodsDetail': 'Goods Detail',
    'output.modal.subtitle.deliveryInfo': 'Delivery Information',
    'output.modal.label.deliveryAddress': 'Delivery Address',
    'output.modal.label.deliveryMethod': 'Delivery Method',
    'output.modal.placeholder.logisticsCompany': 'Please enter logistics company name',
    'output.modal.placeholder.logisticsNo': 'Please enter logistics number',
    'output.modal.message.atLeastOneProduct': 'Please output at least one product!',
    'output.modal.label.outputTime': 'Output Time',
    'output.modal.label.operator': 'Operator',
    'output.modal.label.voidTime': 'Void Time',
    'output.modal.button.voided': 'Voided',
    'output.modal.button.void': 'Void',
    'output.modal.confirm.void': 'Are you sure to void?',
    'output.detail.button.print': 'Print',
    'output.detail.label.businessOrderNo': 'Business Order No.',
    'output.detail.label.outputType': 'Output Type',
    'output.detail.label.notifyOutputTime': 'Notify Output Time',
    'output.detail.label.outputCompleteTime': 'Output Complete Time',
    'output.detail.label.outputWarehouse': 'Output Warehouse',
    'output.detail.label.outputQuantity': 'Output Quantity',
    'output.detail.label.recipient': 'Recipient',
    'output.detail.label.deliveryAddress': 'Delivery Address',
    'output.detail.label.deliveryMethod': 'Delivery Method',
    'output.detail.label.deliveryCompany': 'Delivery Company',
    'output.detail.label.logisticsNo': 'Logistics No.',
    'output.detail.subtitle.goodsDetail': 'Goods Detail',
    'output.detail.subtitle.deliveryInfo': 'Delivery Information',
    'output.detail.columns.itemCode': 'Item Code',
    'output.detail.columns.itemName': 'Item Name',
    'output.detail.columns.oeNo': 'OE No.',
    'output.detail.columns.brandPartNo': 'Brand Part No.',
    'output.detail.columns.brand': 'Brand',
    'output.detail.columns.category': 'Category',
    'output.detail.columns.unit': 'Unit',
    'output.detail.columns.plannedOutput': 'Planned Output',
    'output.detail.columns.outputted': 'Outputted',
    'output.detail.columns.currentOutput': 'Current Output',
    'output.detail.columns.location': 'Location',
    'output.detail.validation.exceedsWaitingQuantity': 'Cannot exceed waiting quantity',
    'input.list.title': 'Input Order List',
    'input.list.button.input': 'Input',
    'input.list.button.inputRecord': 'Input Record',
    'input.list.label.businessOrderNo': 'Business Order No.',
    'input.list.label.inputOrderNo': 'Input Order No.',
    'input.list.label.goodsInfo': 'Goods Information',
    'input.list.label.inputType': 'Input Type',
    'input.list.label.inputStatus': 'Input Status',
    'input.list.label.notifyInputTime': 'Notify Input Time',
    'input.list.label.inputCompleteTime': 'Input Complete Time',
    'input.list.label.inputWarehouse': 'Input Warehouse',
    'input.list.label.inputQuantity': 'Input Quantity',
    'input.list.label.sender': 'Sender',
    'input.list.placeholder.goodsNameOrCode': 'Goods Name/Code',
    'input.modal.title.input': 'Input',
    'input.modal.title.inputRecord': 'Input Record',
    'input.modal.button.confirmInput': 'Confirm Input',
    'input.modal.subtitle.goodsDetail': 'Goods Detail',
    'input.modal.message.atLeastOneProduct': 'Please input at least one product',
    'input.modal.label.inputTime': 'Input Time',
    'input.modal.label.operator': 'Operator',
    'input.modal.label.voidTime': 'Void Time',
    'input.modal.button.voided': 'Voided',
    'input.modal.button.void': 'Void',
    'input.modal.confirm.void': 'Are you sure to void?',
    'input.detail.button.print': 'Print',
    'input.detail.label.businessOrderNo': 'Business Order No.',
    'input.detail.label.inputType': 'Input Type',
    'input.detail.label.notifyInputTime': 'Notify Input Time',
    'input.detail.label.inputCompleteTime': 'Input Complete Time',
    'input.detail.label.inputWarehouse': 'Input Warehouse',
    'input.detail.label.inputQuantity': 'Input Quantity',
    'input.detail.label.sender': 'Sender',
    'input.detail.subtitle.goodsDetail': 'Goods Detail',
    'input.detail.columns.itemCode': 'Item Code',
    'input.detail.columns.itemName': 'Item Name',
    'input.detail.columns.oeNo': 'OE No.',
    'input.detail.columns.brandPartNo': 'Brand Part No.',
    'input.detail.columns.brand': 'Brand',
    'input.detail.columns.category': 'Category',
    'input.detail.columns.unit': 'Unit',
    'input.detail.columns.plannedInput': 'Planned Input',
    'input.detail.columns.inputted': 'Inputted',
    'input.detail.columns.currentInput': 'Current Input',
    'input.detail.columns.location': 'Location',
    'input.detail.validation.exceedsWaitingQuantity': 'Cannot exceed waiting quantity',
    'transfer.list.title': 'Transfer Order List',
    'transfer.list.button.newTransfer': 'New Transfer',
    'transfer.list.button.edit': 'Edit',
    'transfer.list.label.transferOrderNo': 'Transfer Order No.',
    'transfer.list.label.outStore': 'Out Store',
    'transfer.list.label.outWarehouse': 'Out Warehouse',
    'transfer.list.label.inStore': 'In Store',
    'transfer.list.label.inWarehouse': 'In Warehouse',
    'transfer.list.label.documentStatus': 'Document Status',
    'transfer.list.label.createTime': 'Create Time',
    'transfer.list.label.creator': 'Creator',
    'transfer.list.label.remarks': 'Remarks',
    'transfer.operation.title': 'Transfer Operation',
    'transfer.operation.label.outStore': 'Out Store',
    'transfer.operation.label.outWarehouse': 'Out Warehouse',
    'transfer.operation.label.inStore': 'In Store',
    'transfer.operation.label.inWarehouse': 'In Warehouse',
    'transfer.operation.subtitle.transferGoods': 'Transfer Goods',
    'transfer.operation.label.transferOrderNo': 'Transfer Order No.',
    'transfer.operation.label.documentStatus': 'Document Status',
    'transfer.operation.button.addGoods': 'Add Goods',
    'transfer.operation.placeholder.goodsCodeOrName': 'Goods Code/Name',
    'transfer.operation.label.transferRemarks': 'Transfer Remarks',
    'transfer.operation.placeholder.transferRemarks': 'Please enter, max 100 characters',
    'transfer.operation.checkbox.directStockOut': 'Direct Stock Out',
    'transfer.operation.button.cancel': 'Cancel',
    'transfer.operation.button.submit': 'Submit',
    'transfer.operation.modal.title.addGoods': 'Add Goods',
    'transfer.operation.message.warehouseSame': 'In warehouse and out warehouse cannot be the same!',
    'transfer.operation.message.addGoods': 'Please add goods',
    'transfer.operation.message.fillQuantity': 'Please fill in quantity',
    'transfer.operation.message.deleteSuccess': 'Delete successful',
    'transfer.detail.title': 'Transfer Detail',
    'transfer.detail.button.void': 'Void',
    'transfer.detail.button.edit': 'Edit',
    'transfer.detail.button.oneClickStockOut': 'One-Click Stock Out',
    'transfer.detail.button.oneClickStockIn': 'One-Click Stock In',
    'transfer.detail.confirm.void': 'Are you sure to void?',
    'transfer.detail.label.outStore': 'Out Store',
    'transfer.detail.label.outWarehouse': 'Out Warehouse',
    'transfer.detail.label.inStore': 'In Store',
    'transfer.detail.label.inWarehouse': 'In Warehouse',
    'transfer.detail.label.createTime': 'Create Time',
    'transfer.detail.label.creator': 'Creator',
    'transfer.detail.label.remarks': 'Remarks',
    'transfer.detail.subtitle.transferGoods': 'Transfer Goods',
    'transfer.detail.columns.itemCode': 'Item Code',
    'transfer.detail.columns.itemName': 'Item Name',
    'transfer.detail.columns.oeNo': 'OE No.',
    'transfer.detail.columns.brandPartNo': 'Brand Part No.',
    'transfer.detail.columns.brand': 'Brand',
    'transfer.detail.columns.category': 'Category',
    'transfer.detail.columns.unit': 'Unit',
    'transfer.detail.columns.outStock': 'Out Stock',
    'transfer.detail.columns.inStock': 'In Stock',
    'transfer.detail.columns.transferQuantity': 'Transfer Quantity',
    'transfer.detail.columns.outLocation': 'Out Location',
    'transfer.detail.columns.inLocation': 'In Location',
    'transfer.detail.columns.operation': 'Operation',
    'transfer.detail.button.delete': 'Delete',
    'transfer.detail.validation.transferQuantityRequired': 'Transfer quantity cannot be empty',
    'transfer.detail.validation.transferQuantityMin': 'Transfer quantity cannot be less than 1',
    'transfer.detail.validation.transferQuantityMax': 'Transfer quantity cannot exceed out stock',
    'inventory.list.title': 'Inventory Management',
    'inventory.list.label.totalInventory': 'Total Inventory',
    'inventory.list.label.totalCostPrice': 'Total Inventory Value',
    'inventory.list.button.setSafetyInventory': 'Set Safety Stock',
    'inventory.list.button.setLocation': 'Set Location',
    'inventory.list.button.printLabel': 'Print Label',
    'inventory.list.button.export': 'Export',
    'inventory.list.button.importInitialInventory': 'Import Initial Inventory',
    'inventory.list.button.save': 'Save',
    'inventory.list.message.saveSuccess': 'Save successful!',
    'inventory.list.label.goodsInfo': 'Goods Information',
    'inventory.list.placeholder.goodsSearch': 'Goods Name/Code/OE/Brand Part No.',
    'inventory.list.label.itemCode': 'Item Code',
    'inventory.list.label.itemName': 'Item Name',
    'inventory.list.label.oeNo': 'OE No.',
    'inventory.list.label.brandPartNo': 'Brand Part No.',
    'inventory.list.label.brand': 'Brand',
    'inventory.list.label.category': 'Category',
    'inventory.list.label.unit': 'Unit',
    'inventory.list.label.inventoryWarning': 'Inventory Warning',
    'inventory.list.label.inventoryStatus': 'Inventory Status',
    'inventory.list.label.locationStatus': 'Location Status',
    'inventory.list.label.warehouse': 'Warehouse',
    'inventory.list.label.totalInventoryNum': 'Total Inventory',
    'inventory.list.label.lockedNum': 'Locked Quantity',
    'inventory.list.label.availableNum': 'Available Quantity',
    'inventory.list.label.costPrice': 'Cost Price',
    'inventory.list.label.totalCostAmount': 'Total Inventory Value',
    'inventory.list.label.lowerLimit': 'Lower Limit',
    'inventory.list.label.upperLimit': 'Upper Limit',
    'inventory.list.label.location': 'Location',
    'inventory.list.button.inventoryFlow': 'Inventory Flow',
    'inventory.detail.title': 'Inventory Flow',
    'inventory.detail.label.changeTime': 'Change Time',
    'inventory.detail.label.changeType': 'Change Type',
    'inventory.detail.label.warehouse': 'Warehouse',
    'inventory.detail.label.changeNum': 'Change Quantity',
    'inventory.detail.label.businessOrderNo': 'Business Order No.',
    'transfer.operation.goodsModal.title.goodsList': 'Goods List',
    'transfer.operation.goodsModal.checkbox.onlyWithStock': 'Only show with stock',
    'transfer.operation.goodsModal.label.goodsInfo': 'Goods Information',
    'transfer.operation.goodsModal.placeholder.goodsSearch': 'Goods Name/Code/OE/Brand Part No./Mnemonic',
    'transfer.operation.goodsModal.tooltip.goodsSearch': 'Goods Name/Code/OE/Brand Part No./Mnemonic',
    'transfer.operation.goodsModal.columns.itemCode': 'Item Code',
    'transfer.operation.goodsModal.columns.itemName': 'Item Name',
    'transfer.operation.goodsModal.columns.oeNo': 'OE No.',
    'transfer.operation.goodsModal.columns.brandPartNo': 'Brand Part No.',
    'transfer.operation.goodsModal.columns.brand': 'Brand',
    'transfer.operation.goodsModal.columns.category': 'Category',
    'transfer.operation.goodsModal.columns.unit': 'Unit',
    'transfer.operation.goodsModal.columns.outWarehouseStock': 'Out Warehouse Stock',
    'check.operation.title': 'Check Operation',
    'check.operation.label.checkWarehouse': 'Check Warehouse',
    'check.operation.placeholder.selectCheckWarehouse': 'Please select check warehouse',
    'check.operation.label.checkMode': 'Check Mode',
    'check.operation.placeholder.selectCheckMode': 'Please select check mode',
    'check.operation.subtitle.checkGoods': 'Check Goods',
    'check.operation.label.checkOrderNo': 'Check Order No.',
    'check.operation.label.documentStatus': 'Document Status',
    'check.operation.label.checkType': 'Check Type',
    'check.operation.button.addGoods': 'Add Goods',
    'check.operation.button.fullCheck': 'Full Check',
    'check.operation.button.fullCheckWithStock': 'Full Check with Stock',
    'check.operation.checkbox.onlyUnchecked': 'Only show unchecked',
    'check.operation.checkbox.onlyDifference': 'Only show differences',
    'check.operation.placeholder.goodsCodeOrName': 'Goods Code/Name',
    'check.operation.label.checkRemarks': 'Check Remarks',
    'check.operation.placeholder.checkRemarks': 'Please enter, max 100 characters',
    'check.operation.button.cancel': 'Cancel',
    'check.operation.button.submitCheck': 'Submit Check',
    'check.operation.columns.itemCode': 'Item Code',
    'check.operation.columns.itemName': 'Item Name',
    'check.operation.columns.oeNo': 'OE',
    'check.operation.columns.brandPartNo': 'Brand Part No.',
    'check.operation.columns.brand': 'Brand',
    'check.operation.columns.category': 'Category',
    'check.operation.columns.unit': 'Unit',
    'check.operation.columns.inventoryQuantity': 'Inventory Quantity',
    'check.operation.columns.checkInventory': 'Check Inventory',
    'check.operation.columns.differenceQuantity': 'Difference Quantity',
    'check.operation.confirm.delete': 'Confirm delete?',
    'check.operation.button.delete': 'Delete',
    'check.operation.validation.checkInventoryRequired': 'Check inventory must be greater than or equal to zero!',
    'check.operation.goodsModal.title.goodsList': 'Goods List',
    'check.operation.goodsModal.checkbox.onlyWithStock': 'Only show with stock',
    'check.operation.goodsModal.label.goodsInfo': 'Goods Information',
    'check.operation.goodsModal.placeholder.goodsSearch': 'Goods Name/Code/OE/Brand Part No./Mnemonic',
    'check.operation.goodsModal.tooltip.goodsSearch': 'Goods Name/Code/OE/Brand Part No./Mnemonic',
    'check.operation.goodsModal.columns.itemCode': 'Item Code',
    'check.operation.goodsModal.columns.itemName': 'Item Name',
    'check.operation.goodsModal.columns.oeNo': 'OE No.',
    'check.operation.goodsModal.columns.brandPartNo': 'Brand Part No.',
    'check.operation.goodsModal.columns.brand': 'Brand',
    'check.operation.goodsModal.columns.category': 'Category',
    'check.operation.goodsModal.columns.unit': 'Unit',
    'check.operation.goodsModal.columns.location': 'Location',
};
