import ProFormObject, { ObjectType } from '@/components/ProFormItem/ProFormObject';
import type { ReceivedEntity } from '@/pages/finance/receive/types/ReceivedEntity';
import { accountListQuerySimple, queryStoreByAccount } from '@/pages/system/user/services';
import type { ProColumns, ProFormInstance } from '@ant-design/pro-components';
import type { IntlShape } from '@umijs/max';
import { MoneyFomat } from '..';
import { ReceivedStatusEnum } from '../types/ReceivedEntityEnum';

export const getTableColumns = (intl: IntlShape, form?: ProFormInstance): ProColumns<ReceivedEntity>[] => [
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.store' }),
    dataIndex: 'storeName',
    width: 120,
    valueType: 'select',
    fieldProps: {
      mode: 'multiple',
      maxTagCount: 3,
      showSearch: true,
    },
    formItemProps: {
      name: 'storeIdList',
    },
    request: async () => {
      const data = await queryStoreByAccount({});
      return data?.map(({ id, name }) => ({
        value: id,
        label: name,
      }));
    },
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.receiveTime' }),
    dataIndex: 'businessTimeQuery',
    width: 140,
    valueType: 'dateRange',
    hideInTable: true,
    search: {
      transform: (value: any) => {
        return {
          startBusinessTime: value[0],
          endBusinessTime: value[1],
        };
      },
    },
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.receiveTime' }),
    dataIndex: 'businessTime',
    width: 140,
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.customer' }),
    dataIndex: 'buyerName',
    width: 100,
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.customer' }),
    dataIndex: 'buyerId',
    hideInTable: true,
    fieldProps: {
      showSearch: true,
    },
    renderFormItem: (props) => {
      return <div>
        <ProFormObject
          form={form}
          objects={[ObjectType.Customer, ObjectType.OtherCompany]}
          fieldsName={{
            fieldType: 'buyerType',
            fieldName: 'buyerName',
            fieldId: 'buyerId',
          }}
        />
      </div>
    },
    search: {
      transform: (value: any) => {
        return {
          buyerId: value
        };
      },
    },
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.receivedAccount' }),
    dataIndex: 'receivedAccountName',
    search: false,
    width: 100,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.receivedAmount' }),
    dataIndex: 'totalReceivedAmountYuan',
    search: false,
    width: 100,
    renderText: (text) => <MoneyFomat money={text} />,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.writeOffAmount' }),
    dataIndex: 'writeOffAmount',
    search: false,
    width: 100,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.advanceAmount' }),
    dataIndex: 'advanceAmountYuan',
    search: false,
    width: 100,
    renderText: (text) => <MoneyFomat money={text} />,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.creator' }),
    dataIndex: 'createPerson',
    width: 60,
    search: false,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.status' }),
    dataIndex: 'status',
    valueType: 'select',
    width: 120,
    valueEnum: ReceivedStatusEnum,
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.creator' }),
    dataIndex: 'createPerson',
    key: 'createPerson',
    search: true,
    width: 60,
    valueType: 'select',
    hideInTable: true,
    fieldProps: {
      showSearch: true,
      fieldNames: { label: 'name', value: 'id' },
    },
    request: (query: any) => {
      return accountListQuerySimple({ name: query.keyWords });
    },
  },
  {
    title: intl.formatMessage({ id: 'finance.receive.columns.remark' }),
    dataIndex: 'remark',
    search: false,
    width: 100,
    ellipsis: true,
  },
];
