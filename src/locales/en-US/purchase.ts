export default {
  'external.title': 'External Purchase Order',
  'external.label.supplier': 'Supplier',
  'external.placeholder.selectSupplier': 'Please select',
  'external.label.purchaseStore': 'Purchase Store',
  'external.placeholder.selectPurchaseStore': 'Please select',
  'external.label.receiveWarehouse': 'Receive Warehouse',
  'external.subtitle.purchaseOrderDetail': 'Purchase Order Detail',
  'external.label.purchaseOrderNo': 'Purchase Order No.',
  'external.label.purchaseStatus': 'Purchase Status',
  'external.label.remarks': 'Remarks',
  'external.placeholder.remarks': 'Please enter, max 100 characters',
  'external.label.totalPurchaseQuantity': 'Total Purchase Quantity',
  'external.label.totalPurchaseAmount': 'Total Purchase Amount',
  'external.button.confirmSettlement': 'Confirm Settlement',
  'external.button.oneClickInStock': 'One-Click In Stock',
  'external.button.submitAndPrint': 'Submit and Print',
  'external.button.submitPurchaseOrder': 'Submit Purchase Order',
  'external.button.batchImport': 'Batch Import',
  'external.confirm.delete': 'Confirm delete?',
  'external.button.delete': 'Delete',
  'external.message.completeRequiredInfo': 'Please complete required information!',
  'external.message.deleteSuccess': 'Delete successful',
  'external.message.addSuccess': 'Add successful',
  'external.import.taskDesc': 'Purchase detail import',
  'external.columns.index': 'Index',
  'external.columns.itemName': 'Item Name',
  'external.columns.itemCode': 'Item Code',
  'external.columns.oeNo': 'OE',
  'external.columns.brandPartNo': 'Brand Part No.',
  'external.columns.brand': 'Brand',
  'external.columns.category': 'Category',
  'external.columns.originRegion': 'Origin Region',
  'external.columns.specification': 'Specification',
  'external.columns.vehicleRemark': 'Vehicle Remark',
  'external.columns.itemRemark': 'Item Remark',
  'external.columns.unit': 'Unit',
  'external.columns.localInventory': 'Local Inventory',
  'external.columns.inventoryLowerLimit': 'Purchase Inventory Lower Limit',
  'external.columns.inventoryUpperLimit': 'Purchase Inventory Upper Limit',
  'external.columns.location': 'Location',
  'external.columns.subtotal': 'Subtotal',
  'external.columns.purchasePrice': 'Purchase Price',
  'external.columns.purchaseQuantity': 'Purchase Quantity',
  'external.columns.operation': 'Operation',
  'external.columns.totalInventory': 'Total Inventory',
  'external.columns.preTaxSubtotal': 'Pre-Tax Subtotal',
  'external.columns.afterTaxSubtotal': 'After-Tax Subtotal',
  'external.validation.minAmount': 'Minimum amount 0.01',
  'external.label.supplierPayable': 'Supplier Payable',
  'external.button.viewDetails': 'View Details',

  // Purchase List Page
  'list.payStatus.unBalance': 'Unbalanced',
  'list.payStatus.partBalance': 'Balanced',
  'list.button.addPurchase': 'Add Purchase',
  'list.button.export': 'Export',
  'list.button.import': 'Import',
  'list.export.taskDesc': 'Purchase Order List Export',
  'list.import.taskDesc': 'Purchase Order List Import',
  'list.message.submitSuccess': 'Submit successful',

  // Purchase List Table Columns
  'list.columns.orderNo': 'Purchase Order No.',
  'list.columns.sourceNo': 'Reference No.',
  'list.columns.supplierName': 'Supplier Name',
  'list.columns.goodsInfo': 'Product Information',
  'list.columns.goodsInfo.placeholder': 'Product Code/Product Name/OE/Brand Part No.',
  'list.columns.purchaseStore': 'Purchase Store',
  'list.columns.receiveWarehouse': 'Receive Warehouse',
  'list.columns.purchaseQuantity': 'Purchase Quantity',
  'list.columns.purchaseAmount': 'Purchase Amount',
  'list.columns.purchaseUser': 'Creator',
  'list.columns.payType': 'Settlement Method',
  'list.columns.payStatus': 'Settlement Status',
  'list.columns.balanceStatus': 'Payment Status',
  'list.columns.orderTime': 'Purchase Time',
  'list.columns.orderStatus': 'Document Status',
  'list.columns.currency': 'Currency',
  'list.tag.replenishment': 'Replenishment',
  'list.button.edit': 'Edit',
  'list.button.void': 'Void',
  'list.button.withdraw': 'Withdraw',
  'list.button.againOrder': 'Order Again',
  'list.confirm.void': 'Confirm to void?',
  'list.confirm.withdraw': 'Confirm to withdraw?',

  // Purchase Detail Page
  'detail.title.etcOrderNo': 'ETC Order No.',
  'detail.button.edit': 'Edit',
  'detail.button.void': 'Void',
  'detail.button.withdraw': 'Withdraw',
  'detail.button.print': 'Print',
  'detail.button.printLabel': 'Print Label',
  'detail.button.confirmSettlement': 'Confirm Settlement',
  'detail.button.oneClickInStock': 'One-Click In Stock',
  'detail.button.audit': 'Audit',
  'detail.button.afterSales': 'Initiate After Sales',
  'detail.button.afterSalesRecord': 'After Sales Record',
  'detail.button.modify': 'Modify',
  'detail.confirm.void': 'Confirm to void?',
  'detail.confirm.withdraw': 'Confirm to withdraw?',
  'detail.confirm.oneClickInStock': 'Confirm one-click in stock?',

  // Purchase Detail Labels
  'detail.label.supplier': 'Supplier',
  'detail.label.orderStatus': 'Order Status',
  'detail.label.purchaseAmount': 'Purchase Amount',
  'detail.label.payType': 'Payment Type',
  'detail.label.purchaseStore': 'Purchase Store',
  'detail.label.receiveWarehouse': 'Receive Warehouse',
  'detail.label.orderTime': 'Order Time',
  'detail.label.creator': 'Creator',
  'detail.label.purchaseRemark': 'Purchase Remark',
  'detail.label.auditor': 'Auditor',
  'detail.label.auditTime': 'Audit Time',
  'detail.label.remark': 'Remark',

  // Purchase Detail Sections
  'detail.section.logisticsDetail': 'Logistics Detail',
  'detail.section.goodsDetail': 'Product Detail',
  'detail.button.collapse': 'Collapse',
  'detail.button.viewMore': 'View More',

  // Purchase Detail Summary
  'detail.summary.totalQuantity': 'Total Quantity',
  'detail.summary.totalAmount': 'Total Amount',
  'detail.summary.freight': 'Freight',
  'detail.summary.totalPurchaseAmount': 'Total Purchase Amount',

  // Purchase Detail Table Columns
  'detail.columns.itemName': 'Item Name',
  'detail.columns.itemCode': 'Item Code',
  'detail.columns.oe': 'OE',
  'detail.columns.brandPartNo': 'Brand Part No.',
  'detail.columns.brand': 'Brand',
  'detail.columns.category': 'Category',
  'detail.columns.unit': 'Unit',
  'detail.columns.purchasePrice': 'Purchase Price',
  'detail.columns.purchaseQuantity': 'Purchase Quantity',
  'detail.columns.subtotal': 'Subtotal',
  'detail.columns.operation': 'Operation',

  // Audit Modal
  'audit.modal.title': 'Audit',
  'audit.option.approve': 'Approve',
  'audit.option.reject': 'Reject',
  'audit.placeholder.reason': 'Please enter rejection reason',
  'audit.placeholder.remark': 'Please enter',
  'audit.validation.selectResult': 'Please select audit result',
  'audit.validation.rejectReason': 'Please enter rejection reason',

  // After Sales Record Modal
  'afterSales.modal.title': 'After Sales Record',
  'afterSales.button.viewDetail': 'View Detail',
  'afterSales.validation.selectVehicle': 'Please select vehicle',

  // Warehouse Modal
  'warehouse.modal.title': 'Receive Warehouse',
  'warehouse.label.receiveWarehouse': 'Receive Warehouse',
  'warehouse.placeholder.selectWarehouse': 'Please select receive warehouse',
  'warehouse.validation.selectWarehouse': 'Please select receive warehouse',

  // Capital Flow
  'capitalFlow.title': 'Settlement Records',
  'capitalFlow.columns.settlementTime': 'Settlement Time',
  'capitalFlow.columns.settlementAccount': 'Settlement Account',
  'capitalFlow.columns.settlementAmount': 'Settlement Amount',

  // Operation Detail
  'operationDetail.title': 'Operation Records',
  'operationDetail.columns.operationNode': 'Operation Node',
  'operationDetail.columns.operationTime': 'Operation Time',
  'operationDetail.columns.operator': 'Operator',

  // Order Status
  'orderStatus.draft': 'Draft',
  'orderStatus.auditing': 'Auditing',
  'orderStatus.confirm': 'Confirmed',
  'orderStatus.toArrival': 'To Arrival',
  'orderStatus.received': 'Received',
  'orderStatus.complete': 'Complete',
  'orderStatus.close': 'Closed',
  'orderStatus.reject': 'Rejected',

  // Pay Type Status
  'payType.cash': 'Cash',
  'payType.hangingAccounts': 'Credit',

  // Balance Status
  'balanceStatus.unBalance': 'Unpaid',
  'balanceStatus.partBalance': 'Partially Paid',
  'balanceStatus.allBalance': 'Paid',

  // Returns List
  'returns.list.title': 'Purchase Returns List',
  'returns.list.button.add': 'Add Purchase Return',
  'returns.list.button.export': 'Export',
  'returns.list.export.taskDesc': 'Purchase Returns Export',
  'returns.list.columns.returnOrderNo': 'Return Order No.',
  'returns.list.columns.supplierName': 'Supplier Name',
  'returns.list.columns.returnTime': 'Return Time',
  'returns.list.columns.orderStatus': 'Order Status',
  'returns.list.columns.returnStore': 'Return Store',
  'returns.list.columns.outWarehouse': 'Outbound Warehouse',
  'returns.list.columns.returnAmount': 'Return Amount',
  'returns.list.columns.payType': 'Payment Type',
  'returns.list.columns.payStatus': 'Payment Status',
  'returns.list.columns.completeTime': 'Complete Time',
  'returns.list.columns.creator': 'Creator',
  'returns.list.search.productInfo': 'Product Info',
  'returns.list.search.productInfo.placeholder':
    'Product Name/Code/OE/Brand Part No./Mnemonic/Custom Code',
  'returns.list.search.productInfo.tooltip':
    'Product Name/Code/OE/Brand Part No./Mnemonic/Custom Code',
  'returns.list.button.edit': 'Edit',
  'returns.list.button.void': 'Void',
  'returns.list.button.withdraw': 'Withdraw',
  'returns.list.confirm.void': 'Confirm to void?',
  'returns.list.confirm.withdraw': 'Confirm to withdraw?',

  // Return Order Status
  'returnOrderStatus.draft': 'Draft',
  'returnOrderStatus.toOutbound': 'To Outbound',
  'returnOrderStatus.outbound': 'Outbound',
  'returnOrderStatus.complete': 'Complete',
  'returnOrderStatus.close': 'Closed',

  // Supplier List
  'supplier.list.title': 'Supplier List',
  'supplier.list.button.add': 'Add Supplier',
  'supplier.list.button.import': 'Import',
  'supplier.list.button.export': 'Export',
  'supplier.list.import.taskDesc': 'Supplier Import',
  'supplier.list.export.taskDesc': 'Supplier Export',
  'supplier.list.modal.title.add': 'Add Supplier',
  'supplier.list.modal.title.edit': 'Edit Supplier',
  'supplier.list.columns.supplierCode': 'Supplier Code',
  'supplier.list.columns.supplierInfo': 'Supplier Info',
  'supplier.list.columns.supplierInfo.placeholder': 'Supplier Code/Name/Short Name',
  'supplier.list.columns.supplierName': 'Supplier Name',
  'supplier.list.columns.shortName': 'Short Name',
  'supplier.list.columns.defaultContact': 'Default Contact',
  'supplier.list.columns.defaultContactPhone': 'Default Contact Phone',
  'supplier.list.columns.defaultAddress': 'Default Address',
  'supplier.list.columns.status': 'Status',
  'supplier.list.columns.createTime': 'Create Time',
  'supplier.list.columns.abn': 'ABN',
  'supplier.list.columns.settleType': 'Settle Type',
  'supplier.list.columns.financialEmail': 'Financial Email',
  'supplier.list.search.contact': 'Contact',
  'supplier.list.search.contactPhone': 'Contact Phone',
  'supplier.list.button.edit': 'Edit',
  'supplier.list.confirm.statusChange': 'Confirm to {action}?',

  // Supplier Status
  'supplierStatus.enable': 'Enable',
  'supplierStatus.disable': 'Disable',

  // Supplier Detail
  'supplier.detail.title': 'Supplier Detail',
  'supplier.detail.basic.supplierCode': 'Supplier Code',
  'supplier.detail.basic.shortName': 'Short Name',
  'supplier.detail.basic.remark': 'Remark',
  'supplier.detail.contact.title': 'Contact Information',
  'supplier.detail.contact.defaultContact': 'Default Contact',
  'supplier.detail.contact.contactPhone': 'Contact Phone',
  'supplier.detail.contact.qq': 'QQ',
  'supplier.detail.contact.wechat': 'WeChat',
  'supplier.detail.contact.email': 'Email',
  'supplier.detail.address.title': 'Address Information',
  'supplier.detail.address.addressNumber': 'Address {number}',
  'supplier.detail.address.defaultAddress': 'Default Address',
  'supplier.detail.address.region': 'Region',
  'supplier.detail.address.detailAddress': 'Detail Address',
  'supplier.detail.address.contactPerson': 'Contact Person',
  'supplier.detail.address.contactPhone': 'Contact Phone',
  'supplier.detail.settlement.title': 'Settlement Information',
  'supplier.detail.settlement.initAccount': 'Initial Payable',

  // Supplier Operation
  'supplier.operation.basic.title': 'Basic Information',
  'supplier.operation.basic.supplierName': 'Supplier Name',
  'supplier.operation.basic.supplierCode': 'Supplier Code',
  'supplier.operation.basic.shortName': 'Short Name',
  'supplier.operation.basic.remark': 'Remark',
  'supplier.operation.basic.remark.placeholder': 'Please enter, max 100 characters',
  'supplier.operation.contact.title': 'Contact Information',
  'supplier.operation.contact.add': 'Add Contact',
  'supplier.operation.contact.columns.defaultContact': 'Default Contact',
  'supplier.operation.contact.columns.contactPerson': 'Contact Person',
  'supplier.operation.contact.columns.contactPhone': 'Contact Phone',
  'supplier.operation.contact.columns.post': 'Position',
  'supplier.operation.contact.columns.email': 'Email',
  'supplier.operation.contact.columns.remark': 'Remark',
  'supplier.operation.contact.button.edit': 'Edit',
  'supplier.operation.contact.button.delete': 'Delete',
  'supplier.operation.contact.confirm.delete': 'Confirm to delete?',
  'supplier.operation.address.title': 'Address Information',
  'supplier.operation.address.add': 'Add Address',
  'supplier.operation.address.columns.defaultAddress': 'Default Address',
  'supplier.operation.address.columns.region': 'Province/City/District',
  'supplier.operation.address.columns.detailAddress': 'Detail Address',
  'supplier.operation.address.columns.contactPerson': 'Contact Person',
  'supplier.operation.address.columns.contactPhone': 'Contact Phone',
  'supplier.operation.address.button.edit': 'Edit',
  'supplier.operation.address.button.delete': 'Delete',
  'supplier.operation.address.confirm.delete': 'Confirm to delete?',
  'supplier.operation.settlement.title': 'Settlement Information',
  'supplier.operation.settlement.initAccount': 'Initial Payable',
  'supplier.operation.settlement.settleType': 'Settlement Method',
  'supplier.operation.settlement.gstExcluded': 'GST Excluded',
  'supplier.operation.settlement.isMultiCurrency': 'Multi-currency',
  'supplier.operation.settlement.bankAccount': 'Bank Account',
  'supplier.operation.settlement.addAccount': 'Add Account',
  'supplier.operation.settlement.columns.accountName': 'Account Name',
  'supplier.operation.settlement.columns.bsb': 'BSB',
  'supplier.operation.settlement.columns.accountNumber': 'Account Number',
  'supplier.operation.settlement.columns.swiftCode': 'Swift Code',
  'supplier.operation.basic.abn': 'ABN',
  'supplier.operation.basic.clientCode': 'Client Code',
  'supplier.operation.basic.country': 'Country',
  'supplier.operation.basic.universalEmail': 'Universal Email',
  'supplier.operation.basic.financeEmail': 'Finance Email',
  'supplier.operation.basic.telephone': 'Telephone',
  'supplier.operation.address.columns.postCode': 'Post Code',
  'supplier.operation.address.columns.state': 'State',
  'supplier.operation.address.columns.suburb': 'Suburb',
  'supplier.operation.address.columns.contactName': 'Contact Name',
  'supplier.operation.settlement.currency': 'CNY',

  // Returns Operation
  'returns.operation.form.supplier': 'Supplier',
  'returns.operation.form.supplier.placeholder': 'Please select',
  'returns.operation.form.store': 'Initiating Store',
  'returns.operation.form.store.placeholder': 'Please select',
  'returns.operation.form.warehouse': 'Shipping Warehouse',
  'returns.operation.form.warehouse.placeholder': 'Please select',
  'returns.operation.tab.returnByOrder': 'Return by Purchase Order',
  'returns.operation.tab.returnByOrder.description':
    'Select products from purchase order to return',
  'returns.operation.tab.returnByItem': 'Return by Product',
  'returns.operation.tab.returnByItem.description': 'Select products to return',
  'returns.operation.detail.title': 'Return Order Details',
  'returns.operation.detail.orderNo': 'Return Order No.',
  'returns.operation.detail.status': 'Return Status',
  'returns.operation.detail.delete': 'Delete',
  'returns.operation.form.remark': 'Remark',
  'returns.operation.form.remark.placeholder': 'Please enter, max 100 characters',
  'returns.operation.summary.returnQuantity': 'Return Quantity',
  'returns.operation.summary.returnAmount': 'Return Amount',
  'returns.operation.checkbox.confirmSettlement': 'Confirm Settlement',
  'returns.operation.checkbox.directOutbound': 'Direct Outbound',
  'returns.operation.checkbox.printAfterSubmit': 'Print After Submit',
  'returns.operation.button.submit': 'Submit Purchase Return',
  'returns.operation.message.completeRequired': 'Please complete required information!',
  'returns.operation.message.maxItems': 'Cannot add more than 200 products',
  'returns.operation.message.addSuccess': 'Added successfully',
  'returns.operation.message.fillReturnAmount': 'Please fill in return amount!',
  'returns.operation.message.fillReturnQuantity': 'Please fill in return quantity!',

  // Returns Operation - Order Returns Columns
  'returns.operation.orderColumns.productInfo': 'Product Information',
  'returns.operation.orderColumns.productInfo.placeholder': 'Name/Code/OE/Brand Part No.',
  'returns.operation.orderColumns.purchaseOrderNo': 'Purchase Order No.',
  'returns.operation.orderColumns.purchaseTime': 'Purchase Time',
  'returns.operation.orderColumns.productCode': 'Product Code',
  'returns.operation.orderColumns.productName': 'Product Name',
  'returns.operation.orderColumns.oe': 'OE',
  'returns.operation.orderColumns.brandPartNo': 'Brand Part No.',
  'returns.operation.orderColumns.brand': 'Brand',
  'returns.operation.orderColumns.purchaseStore': 'Purchase Store',
  'returns.operation.orderColumns.receiveWarehouse': 'Receive Warehouse',
  'returns.operation.orderColumns.paymentMethod': 'Payment Method',
  'returns.operation.orderColumns.actualPrice': 'Actual Price',
  'returns.operation.orderColumns.purchaseQuantity': 'Purchase Quantity',
  'returns.operation.orderColumns.returnableQuantity': 'Returnable Quantity',
  'returns.operation.orderColumns.returnAmount': 'Return Amount',
  'returns.operation.orderColumns.returnQuantity': 'Return Quantity',
  'returns.operation.orderColumns.operation': 'Operation',

  // Returns Operation - Detail Columns
  'returns.operation.detailColumns.productCode': 'Product Code',
  'returns.operation.detailColumns.productName': 'Product Name',
  'returns.operation.detailColumns.oe': 'OE',
  'returns.operation.detailColumns.brandPartNo': 'Brand Part No.',
  'returns.operation.detailColumns.brand': 'Brand',
  'returns.operation.detailColumns.purchaseOrderNo': 'Purchase Order No.',
  'returns.operation.detailColumns.purchaseStore': 'Purchase Store',
  'returns.operation.detailColumns.returnAmount': 'Return Amount',
  'returns.operation.detailColumns.returnQuantity': 'Return Quantity',
  'returns.operation.detailColumns.returnReason': 'Return Reason',
  'returns.operation.detailColumns.returnReason.placeholder': 'Please enter',

  // Stock Up List
  'stockUp.list.button.addStockUp': 'Add Stock Up',
  'stockUp.list.menu.stockByInventory': 'Stock Up by Inventory',
  'stockUp.list.menu.stockBySales': 'Stock Up by Sales',
  'stockUp.list.loading.generating': 'Generating stock up suggestion...',
  'stockUp.list.message.generating': 'Stock up suggestion is being generated, please check later',
  'stockUp.list.columns.index': 'Index',
  'stockUp.list.columns.suggestionNo': 'Suggestion No.',
  'stockUp.list.columns.suggestionName': 'Suggestion Name',
  'stockUp.list.columns.status': 'Status',
  'stockUp.list.columns.sumQuantity': 'Product Quantity',
  'stockUp.list.columns.createTime': 'Create Time',
  'stockUp.list.columns.updateTime': 'Update Time',
  'stockUp.list.columns.operation': 'Operation',
  'stockUp.list.button.view': 'View',

  // Stock Up Status
  'stockUpStatus.effective': 'Effective',
  'stockUpStatus.expire': 'Expired',
  'stockUpStatus.generating': 'Generating',
  'stockUpStatus.replenished': 'Replenished',

  // Stock By Inventory Modal
  'stockUp.inventory.modal.title': 'Stock Up by Inventory',
  'stockUp.inventory.modal.alert':
    'The system will query products that meet inventory baseline conditions based on inventory rules. If products have not set inventory upper and lower limits or the suggested replenishment quantity is 0, they will not be displayed in the replenishment results.',
  'stockUp.inventory.form.warehouse': 'Replenishment Warehouse',
  'stockUp.inventory.form.warehouse.placeholder': 'Please select',
  'stockUp.inventory.form.rule': 'Replenishment Rule',
  'stockUp.inventory.form.rule.upperUp': 'Replenish to upper limit when below upper limit',
  'stockUp.inventory.form.rule.lowUp': 'Replenish to upper limit when below lower limit',

  // Stock By Sales Modal
  'stockUp.sales.modal.title': 'Stock Up by Sales',
  'stockUp.sales.modal.alert':
    'The system will query products sold within the specified order time at specified sales stores and replenish based on warehouse settings. You can filter suggestion details based on your replenishment results.',
  'stockUp.sales.form.store': 'Sales Store',
  'stockUp.sales.form.store.placeholder': 'Please select',
  'stockUp.sales.form.orderTime': 'Order Time',
  'stockUp.sales.form.salesStatus': 'Sales Status',
  'stockUp.sales.form.preset.today': 'Today',
  'stockUp.sales.form.preset.recent3Days': 'Recent 3 Days',
  'stockUp.sales.form.preset.recent7Days': 'Recent 7 Days',
  'stockUp.sales.form.preset.recent30Days': 'Recent 30 Days',
  'stockUp.sales.form.preset.thisMonth': 'This Month',
  'stockUp.sales.status.draft': 'Draft',
  'stockUp.sales.status.waitToOutbound': 'Wait to Outbound',
  'stockUp.sales.status.outboundFinish': 'Outbound Finished',
  'stockUp.sales.status.tradeSuccess': 'Completed',
};
