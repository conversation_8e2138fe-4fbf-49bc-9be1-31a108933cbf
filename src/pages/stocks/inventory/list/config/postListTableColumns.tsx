import AuthButton from '@/components/common/AuthButton';
import { queryGoodsPropertyPage } from '@/pages/goods/property/services';
import { queryLocationByWhId } from '@/pages/stocks/location/services';
import { MAX_AMOUNT } from '@/utils/Constants';
import { transformCategoryTree } from '@/utils/transformCategoryTree';
import type { ProColumns } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Space } from 'antd';
import type { ReactNode } from 'react';
import { invLimitStatusOptions } from '../types/HaveInvStatus';
import { haveItemCodeStatusOptions } from '../types/HaveItemCodeStatus';
import { invLimitStatuOptions } from '../types/InvLimitStatus';
import type { InventoryPostEntity } from '../types/inventory.post.entity';
import { SetupTypeStatus } from '../types/setupTypeStatus';

export interface PostListTableColumnsProps {
  handleDetail: (itemId: string, warehouseId: string, warehouseName: string) => void;
  state: boolean;
  setUp: SetupTypeStatus | undefined;
  wareHouseOptions: { label: string; value: string }[];
}

export const PostListTableColumns = (props: PostListTableColumnsProps): ProColumns<InventoryPostEntity>[] => {
  const intl = useIntl();
  return [
    {
      title: intl.formatMessage({ id: 'common.column.index' }),
      valueType: 'index',
      width: 40,
      fixed: 'left',
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.goodsInfo' }),
      dataIndex: 'keyword',
      hideInTable: true,
      fieldProps: {
        placeholder: intl.formatMessage({ id: 'stocks.inventory.list.placeholder.goodsSearch' }),
      },
      order: 6,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.itemCode' }),
      dataIndex: 'itemSn',
      width: 100,
      search: false,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.itemName' }),
      dataIndex: 'itemName',
      ellipsis: true,
      width: 120,
      search: false,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.oeNo' }),
      dataIndex: 'oeNo',
      width: 140,
      search: false,
      ellipsis: true,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.brandPartNo' }),
      dataIndex: 'brandPartNos',
      width: 100,
      search: false,
      ellipsis: true,
      editable: false,
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.brand' }),
      dataIndex: 'brandName',
      width: 100,
      editable: false,
      search: false,
      order: 4,
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.brand' }),
      dataIndex: 'brandIdList',
      order: 4,
      fieldProps: { mode: 'multiple', showSearch: true, maxTagCount: 3 },
      debounceTime: 300,
      hideInTable: true,
      request: (params) => {
        return queryGoodsPropertyPage(
          { brandStatus: '1', pageSize: 999, pageNo: 1, brandName: params.keyWords },
          'brand',
        ).then((result) =>
          result.data?.map((item) => ({ label: item.brandName, value: item.brandId })),
        );
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.category' }),
      dataIndex: 'categoryName',
      width: 100,
      editable: false,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.category' }),
      dataIndex: 'categoryIdList',
      width: 100,
      editable: false,
      order: 3,
      hideInTable: true,
      valueType: 'treeSelect',
      fieldProps: {
        treeCheckable: true,
        maxTagCount: 3,
        filterTreeNode: (text: string, treeNode: any) => treeNode.text?.includes(text),
      },
      request: () => {
        return queryGoodsPropertyPage(
          { pageSize: 999, pageNo: 1, isReturnTree: true },
          'category',
        ).then((result) => transformCategoryTree(result.data));
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.inventoryWarning' }),
      dataIndex: 'invLimitStatusList',
      width: 120,
      editable: false,
      hideInTable: true,
      search: true,
      valueType: 'select',
      valueEnum: invLimitStatuOptions,
      fieldProps: { mode: 'multiple' },
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.inventoryStatus' }),
      dataIndex: 'haveInv',
      width: 120,
      editable: false,
      hideInTable: true,
      valueEnum: invLimitStatusOptions,
      search: true,
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.locationStatus' }),
      dataIndex: 'haveItemCode',
      width: 120,
      editable: false,
      hideInTable: true,
      search: true,
      valueEnum: haveItemCodeStatusOptions,
    },

    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.warehouse' }),
      dataIndex: 'warehouseName',
      key: 'warehouseName',
      width: 120,
      order: 5,
      editable: false,
      valueType: 'select',
      search: {
        transform: (value: any) => {
          return {
            warehouseIdList: value,
          };
        },
      },
      ellipsis: true,
      fieldProps: {
        maxTagCount: 3,
        options: props.wareHouseOptions,
        mode: 'multiple',
      },
    },

    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.totalInventoryNum' }),
      dataIndex: 'inventoryNum',
      width: 70,
      editable: false,
      search: false,
      renderText(text, record) {
        let inventoryNumNode: ReactNode = undefined;
        // const lockedNum = record?.lockedNum;
        // const avaNum = record?.avaNum;
        if (record?.inventoryNum! > record?.upperLimit!) {
          inventoryNumNode = <span className="text-[#F83431]">{text} &#x2191;</span>;
        } else if (record?.inventoryNum! < record?.lowerLimit!) {
          inventoryNumNode = <span className="text-[#33CC47]">{text} &#x2193;</span>;
        } else {
          inventoryNumNode = text
        }
        return inventoryNumNode;
        // return <Space align='center' size={8}>
        //   <span>总数：{inventoryNumNode}</span>
        //   <span>占用：{lockedNum}</span>
        //   <span>可用：{avaNum}</span>
        // </Space>
      },
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.lockedNum' }),
      dataIndex: 'lockedNum',
      search: false,
      editable: false,
      width: 60,
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.availableNum' }),
      dataIndex: 'avaNum',
      width: 60,
      editable: false,
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.lowerLimit' }),
      dataIndex: 'lowerLimit',
      width: 60,
      valueType: 'digit',
      editable: props.state && props.setUp == SetupTypeStatus.SAFETY,
      hideInTable: props.state && props.setUp == SetupTypeStatus.LOCATION,
      fieldProps: (_, config) => {
        return {
          min: 0,
          max: MAX_AMOUNT,
        };
      },
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.upperLimit' }),
      dataIndex: 'upperLimit',
      width: 60,
      valueType: 'digit',
      editable: props.state && props.setUp == SetupTypeStatus.SAFETY,
      hideInTable: props.state && props.setUp == SetupTypeStatus.LOCATION,
      fieldProps: (_, config) => {
        return {
          min: 0,
          max: MAX_AMOUNT,
        };
      },
      search: false,
    },
    {
      title: intl.formatMessage({ id: 'stocks.inventory.list.label.location' }),
      dataIndex: 'locationIdList',
      width: 100,
      editable: props.state && props.setUp == SetupTypeStatus.LOCATION,
      hideInTable: props.state && props.setUp == SetupTypeStatus.SAFETY,
      valueType: 'select',
      fieldProps: { mode: 'multiple', fieldNames: { label: 'code', value: 'id' } },
      search: false,
      key: 'locationIdList',
      ellipsis: true,
      params: (record, column) => {
        return { warehouseIdList: [record.warehouseId] };
      },
      request: async (params) => {
        const data = await queryLocationByWhId({ ...params });
        return data?.warehouseLocationRoList;
      },
    },
    {
      title: intl.formatMessage({ id: 'common.column.operation' }),
      key: 'operation',
      search: false,
      width: 120,
      fixed: 'right',
      editable: false,
      render: (text, record) => (
        <Space>
          <AuthButton
            isHref
            authority="stockLog"
            onClick={() =>
              props.handleDetail(record?.itemId!, record?.warehouseId!, record?.warehouseName!)
            }
          >
            {intl.formatMessage({ id: 'stocks.inventory.list.button.inventoryFlow' })}
          </AuthButton>
        </Space>
      ),
    },
  ] as ProColumns<InventoryPostEntity>[];
};
