import { queryAllCurrency } from '@/pages/finance/tag/services';
import {
  ProForm,
  ProFormDependency,
  ProFormItem,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { InputNumber } from 'antd';

const ProFormCurrency = (props: any) => {
  const { disabled } = props;
  const form = ProForm.useFormInstance();

  return (
    <ProForm.Group
      {...props}
      size={0}
      spaceProps={{
        style: {
          flexWrap: 'nowrap',
        },
      }}
    >
      <div className="flex-1">
        <ProFormSelect
          name="currency"
          label="币种"
          allowClear={false}
          disabled={disabled}
          request={async (query) => {
            const data = await queryAllCurrency({ currencyName: query.keyWords });
            const result = data?.map(({ targetCurrency, rate, currencySymbol }) => ({
              value: targetCurrency,
              label: targetCurrency,
              rate: rate,
              currencySymbol,
            }));
            form.setFieldsValue({
              currency: result[0]?.value,
              currencySymbol: result[0]?.currencySymbol,
              rate: result[0]?.rate,
            });
            return result;
          }}
          onChange={(value, option) => {
            console.log(value, option);
            form.setFieldsValue({ rate: option.rate, currencySymbol: option.currencySymbol });
          }}
        />
      </div>
      <ProFormText name="currencySymbol" hidden />
      <div className="flex-1">
        <ProFormDependency name={['currency']}>
          {({ currency }) => {
            return (
              <ProFormItem label=" " colon={false} name="rate">
                <InputNumber
                  addonBefore="汇率"
                  disabled={disabled || currency == 'AUD'}
                  width={100}
                />
              </ProFormItem>
            );
          }}
        </ProFormDependency>
      </div>
    </ProForm.Group>
  );
};

export default ProFormCurrency;
