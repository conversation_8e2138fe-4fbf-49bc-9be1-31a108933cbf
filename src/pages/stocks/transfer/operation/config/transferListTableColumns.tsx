import AuthButton from '@/components/common/AuthButton';
import type { ProColumns } from '@ant-design/pro-components';
import { Popconfirm } from 'antd';
import type { TransferDetailPostEntity } from '../../detail/types/transfer.detail.post.entity';
import ColumnRender from '@/components/ColumnRender';

export interface TransferListTableColumnsProps {
  handleDeleteItem: (id: string) => void;
}

export const TransferListTableColumns = (props: TransferListTableColumnsProps) =>
  [
    {
      title: '序号',
      valueType: 'index',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      width: 40,
      fixed: 'left',
    },
    {
      title: '商品编码',
      dataIndex: 'itemSn',
      key: 'itemSn',
      readonly: true,
      width: 100,
    },
    {
      title: '商品名称',
      dataIndex: 'itemName',
      key: 'itemName',
      readonly: true,
      width: 140,
    },
    {
      title: 'OE',
      dataIndex: 'oeNo',
      key: 'oeNo',
      readonly: true,
      width: 140,
      renderText: (text: string) => ColumnRender.ArrayColumnRender(text?.split(',')),
    },
    {
      title: '供应商编码',
      dataIndex: 'brandPartNo',
      key: 'brandPartNo',
      readonly: true,
      width: 100,
      renderText: (text: string) => ColumnRender.ArrayColumnRender(text?.split(',')),
    },
    {
      title: '品牌',
      dataIndex: 'brandName',
      key: 'brandName',
      readonly: true,
      width: 100,
    },
    {
      title: '分类',
      dataIndex: 'categoryName',
      key: 'categoryName',
      readonly: true,
      width: 100,
    },
    {
      title: '单位',
      dataIndex: 'unitName',
      key: 'unitName',
      readonly: true,
      width: 50,
    },
    {
      title: '调出仓库存',
      dataIndex: 'inventoryNum',
      key: 'inventoryNum',
      readonly: true,
      width: 100,
      fixed: 'right',
    },
    {
      title: '调拨数量',
      dataIndex: 'transferNum',
      key: 'transferNum',
      valueType: 'digit',
      readonly: false,
      width: 120,
      fixed: 'right',
      fieldProps: (_, config) => {
        return {
          min: 0,
        };
      },
      formItemProps: (_, { entity }) => {
        return {
          rules: [
            {
              validator: async (_, value) => {
                if (value == null) {
                  return Promise.reject(new Error('调拨数量必须大于等于零！'));
                } else {
                  return Promise.resolve();
                }
              },
            },
          ],
        };
      },
    },
    {
      title: '操作',
      editable: false,
      width: 100,
      fixed: 'right',
      render: (text, record) => (
        <Popconfirm title={'确认删除吗'} onConfirm={() => props.handleDeleteItem(record.id)}>
          <AuthButton isHref authority="removeTransferItem">
            删除
          </AuthButton>
        </Popconfirm>
      ),
    },
  ] as ProColumns<TransferDetailPostEntity>[];
