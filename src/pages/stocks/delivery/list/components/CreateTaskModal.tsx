
import ProFormObject, { ObjectType } from '@/components/ProFormItem/ProFormObject';
import { warehouseList } from '@/pages/stocks/warehouse/services';
import { accountListQuerySimple } from '@/pages/system/user/services';
import { ModalForm, ProFormDateTimePicker, ProFormRadio, ProFormSelect, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { useIntl } from '@umijs/max';
import { Flex, Form } from 'antd';
import { useEffect } from 'react';
import { createDelivery, editDelivery, queryDeliveryDetail } from '../../services';
import { DeliveryEntity } from '../../types/Delivery.entity';
import { DeliveryType, DistributionMode, TargetType } from '../../types/delivery.enums';

interface CreateTaskModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  editId?: string; // 编辑时的ID
}

const CreateTaskModal: React.FC<CreateTaskModalProps> = ({ visible, onClose, onSuccess, editId }) => {
  const intl = useIntl();
  const t = (id: string, ...rest: any[]) => intl.formatMessage({ id }, ...rest);
  const [form] = Form.useForm<DeliveryEntity>();

  const loadDeliveryDetail = async () => {
    if (!editId) return;
    try {
      const detail = await queryDeliveryDetail({ id: editId });
      form.setFieldsValue({
        ...detail,
        deliveryTargetType: detail.deliveryTargetType === TargetType.CUSTOMER ? ObjectType.Customer : TargetType.SUPPLIER
      });
    } catch (error) {
      console.error('加载运单详情失败:', error);
    }
  };

  // 当弹框打开且有editId时，加载详情数据
  useEffect(() => {
    if (visible && editId) {
      loadDeliveryDetail();
    } else if (visible && !editId) {
      // 新建时重置表单
      form.resetFields();
    }
  }, [visible, editId]);

  const handleFinish = async (values: DeliveryEntity) => {
    const params = {
      ...values,
      deliveryTargetType: values.deliveryTargetType === ObjectType.Suppler ? 1 : 2
    }
    if (editId) {
      await editDelivery({
        ...params,
        id: editId
      });
    } else {
      await createDelivery(params);
    }
    onSuccess();
    onClose();
    return true;
  };

  return (
    <ModalForm<DeliveryEntity>
      title={editId ? t('common.button.edit') : t('stocks.delivery.list.button.createTask')}
      width={800}
      form={form}
      open={visible}
      onOpenChange={(visible) => {
        if (!visible) {
          onClose();
        }
      }}
      onFinish={handleFinish}
      initialValues={{
        billType: DeliveryType.DELIVERY,
        distributionMode: DistributionMode.SELF_PICKUP,
      }}
      submitter={{
        render: (_, dom) => <Flex justify="center" className='w-full' gap={20}>{dom}</Flex>,
      }}
    >
      <ProFormRadio.Group
        name="billType"
        label={t('stocks.delivery.list.column.taskType')}
        options={[
          { label: t('stocks.delivery.list.billType.delivery'), value: DeliveryType.DELIVERY },
          { label: t('stocks.delivery.list.billType.pickup'), value: DeliveryType.PICKUP },
        ]}
      />
      <ProFormSelect
        name="warehouseId"
        label={t('stocks.delivery.list.column.store')}
        showSearch
        request={async () => {
          const data = await warehouseList({});
          return data?.warehouseSimpleRoList?.map(({ id, warehouseName }) => ({
            value: id,
            label: warehouseName,
          }));
        }}
        onChange={(value, option) => {
          form.setFieldsValue({
            warehouseName: option?.label,
          });
        }}
      />
      <ProFormText name="warehouseName" hidden />
      <ProFormObject
        objects={[ObjectType.Customer, ObjectType.Suppler]}
        label={t('stocks.delivery.list.column.deliveryTarget')}
        form={form}
        fieldsName={{
          fieldType: 'deliveryTargetType',
          fieldName: 'deliveryTargetName',
          fieldId: 'deliveryTargetId',
        }}
      />
      <ProFormText name="origBillNo" label={t('stocks.delivery.list.column.businessNo')} />

      <ProFormSelect
        name="deliveryManId"
        label={t('stocks.delivery.list.column.deliveryMan')}
        showSearch
        request={() => {
          return accountListQuerySimple({}).then((data) => {
            console.log(data);
            return data?.map(({ id, name }) => ({
              value: id,
              label: name,
            }));
          });
        }}
        onChange={(value, option) => {
          form.setFieldsValue({
            deliveryMan: option?.value,
          });
        }}
      />
      <ProFormText name="deliveryMan" hidden />
      <ProFormDateTimePicker name="expectedArrTime" label={t('stocks.delivery.list.column.expectedArrivalTime')} />
      <ProFormTextArea name="remark" label={t('stocks.delivery.list.column.description')} rules={[{ required: true, }]} />
    </ModalForm>
  );
};

export default CreateTaskModal;

