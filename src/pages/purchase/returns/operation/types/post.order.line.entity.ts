export interface PostOrderLineEntity {
  /**
   * 是否有售后记录0-无记录>0有记录
   */
  afterRecordNum?: number;
  /**
   * 商品行金额
   */
  amount?: number;
  /**
   * 品牌id
   */
  brandId?: string;
  /**
   * 品牌name
   */
  brandName?: string;
  /**
   * 供应商编码
   */
  brandPartNo?: string;
  /**
   * 供应商编码,前端下拉展示
   */
  brandPartNoList?: string[];
  /**
   * 品类id
   */
  categoryId?: string;
  /**
   * 品类name
   */
  categoryName?: string;
  /**
   * 采购明细id
   */
  id?: string;
  lineId?: string;
  /**
   * 商品id
   */
  itemId?: string;
  /**
   * 商品编码
   */
  itemSn?: string;
  /**
   * 数量
   */
  num?: number;

  /**
   * 退货数量
   */
  number?: number;
  /**
   * oe号
   */
  oe?: string;
  /**
   * oe号,前端下拉展示
   */
  oeList?: string[];
  /**
   * 采购订单号
   */
  orderNo?: string;
  /**
   * 商品实付单价
   */
  price?: number;
  /**
   * 退款金额
   */
  returnPrice?: number;
  /**
   * 入库数量
   */
  receivedQuantity?: number;
  /**
   * 售后记录
   */
  recordRoList?: RecordRoList[];
  /**
   * 可退货数量
   */
  returnableQuantity?: number;
  /**
   * 退货数量
   */
  returnQuantity?: number;
  /**
   * 商品id
   */
  skuId?: string;
  /**
   * 商品名称
   */
  skuName?: string;
  /**
   * 单位
   */
  unit?: string;
  /**
   * 单位
   */
  purchaseOrderNo?: string;

  returnsId?: string;

  orderTime?: string;
}

export interface RecordRoList {
  /**
   * 售后单id
   */
  returnId?: string;
  /**
   * 售后单号
   */
  returnNo?: string;
  /**
   * 售后时间
   */
  returnTime?: string;
  /**
   * 采购订单号
   */
  subOrderNo?: string;

  /**
   * 门店名称
   */
  storeName?: string;
}
