import { DeliveryEntity } from '../types/delivery.entity';

export type DeliveryListItem = DeliveryEntity;

export interface DeliveryListSearchParams {
  bizBillNo?: string;
  state?: number;
  deliveryMan?: string;
  deliveryManId?: string;
  beginTime?: string;
  expectedArrTime?: string;
  finishTime?: string;
  contactName?: string;
  contactPhone?: string;
  deliveryAddress?: string;
  deliveryTargetName?: string;
  warehouseId?: string;
  warehouseName?: string;
  billType?: number;
  distributionMode?: number;
}
