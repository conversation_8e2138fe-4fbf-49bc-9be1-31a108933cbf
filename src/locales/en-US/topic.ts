export default {
    'decoration.title.pageSetting': 'Page Settings',
    'decoration.title.components': 'Basic Components',
    'decoration.title.pageLayout': 'Page Layout',
    'decoration.label.pageTitle': 'Page Title',
    'decoration.placeholder.pageTitle': 'Please enter page title',
    'decoration.label.isHome': 'Set as Homepage',
    'decoration.label.pageRemark': 'Page Remark',
    'decoration.button.exit': 'Exit Editing',

    'decoration.icon.hotspot': 'Image Hotspot',
    'decoration.icon.imageAd': 'Image Ad',
    'decoration.icon.moduleTitle': 'Module Title',
    'decoration.icon.productList': 'Product List',
    'decoration.icon.coupon': 'Coupon',
    'decoration.icon.container': 'Container',
    'decoration.icon.divider': 'Divider',

    'decoration.tips.dragComponent': 'Drag components from the left to the canvas area',
    'decoration.tips.clickComponent': 'Click on a component to edit its properties',

    'decoration.productList.mock.itemName': 'Here will display product name, up to 2 lines',

    'decoration.divider.padding.horizontal': 'Horizontal Padding',
    'decoration.divider.padding.vertical': 'Vertical Padding',

    'decoration.imageAd.empty': 'Click to edit image ad',
    'decoration.imageAd.upload': 'Add Image',
    'decoration.imageAd.reUpload': 'Replace Image',
    'decoration.imageAd.upload.tips': 'Upload multiple images to display in carousel',

    'decoration.moduleTitle.titleContent': 'Title Content',
    'decoration.moduleTitle.titleContent.placeholder': 'Module Title',
    'decoration.moduleTitle.showSubtitle': 'Show Subtitle',
    'decoration.moduleTitle.subtitleContent.placeholder': 'free shipping',
    'decoration.moduleTitle.subtitleContent': 'Subtitle Content',
    'decoration.moduleTitle.showMore': 'Show More',
    'decoration.moduleTitle.showMore.placeholder': 'View All',
    'decoration.moduleTitle.promptText': 'Prompt Text',
    'decoration.moduleTitle.linkSetting': 'Link Settings',

    'decoration.linkSetting.type.none': 'No Link',
    'decoration.linkSetting.type.product': 'Product',
    'decoration.linkSetting.type.topic': 'Topic Page',
    'decoration.linkSetting.type.coupon': 'Coupon',
    'decoration.linkSetting.type.activity': 'Activity',
    'decoration.linkSetting.type.keyword': 'Keyword',
    'decoration.linkSetting.type.placeholder': 'Please select jump type',
    'decoration.linkSetting.keyword.placeholder': 'Please enter keyword',
    'decoration.linkSetting.select': 'Select {type}',

    'decoration.imageHotspot.placeholder': 'Click to edit image hotspot',
    'decoration.imageHotspot.suggestion': 'Suggested image width 1280px, height unlimited.',
    'decoration.imageHotspot.instructions': 'Drag and create in the image area below, or click the button to add.',
    'decoration.imageHotspot.upload': 'Upload Image',
    'decoration.imageHotspot.reupload': 'Replace Image',
    'decoration.imageHotspot.upload.placeholder': 'Please upload an image first',
    'decoration.imageHotspot.hotspotsLinks': 'Hotspot Links',
    'decoration.imageHotspot.hotspots': 'Hotspots',
    'decoration.imageHotspot.deleteHotspot': 'Delete Hotspot',
    'decoration.imageHotspot.addHotspot': 'Add Hotspot',

    'decoration.coupon': 'Coupon',
    'decoration.coupon.management': 'Coupon Management',
    'decoration.coupon.select': 'Select Coupon',
    'decoration.coupon.claim': 'Claim',
    'decoration.coupon.threshold': '${threshold} to use', // Use when {threshold}{thresholdUnit}
    'decoration.coupon.noCoupons': 'click to add coupon',

    // Product List Settings
    'decoration.productList.settings.conditionsTitle': 'At least one of the following conditions must be set',
    'decoration.productList.settings.productTag': 'Product Tag',
    'decoration.productList.settings.selectTag': 'Please select tags',
    'decoration.productList.settings.onlyCampaignProducts': 'Campaign Products Only',
    'decoration.productList.settings.displayCount': 'Display Count',
    'decoration.productList.settings.display': 'Display',
    'decoration.productList.settings.displayAll': 'Display All',
    'decoration.productList.settings.countPlaceholder': 'Count',
    'decoration.productList.settings.selectProduct': 'Select Products',
    'decoration.productList.settings.selectedCount': 'Selected {count} products',
    'decoration.productList.settings.productSource': 'Product Source',
    'decoration.productList.settings.specifiedProducts': 'Specified Products',
    'decoration.productList.settings.conditionalProducts': 'Conditional Products',
    'decoration.productList.settings.listStyle': 'List Style',
    'decoration.productList.settings.onePerRow': 'One Per Row',
    'decoration.productList.settings.twoPerRow': 'Two Per Row',
    'decoration.productList.settings.horizontalScroll': 'Horizontal Scroll',
    'decoration.productList.settings.type': 'Type',
    'decoration.productList.settings.product': 'Product',
    'decoration.productList.settings.productGroup': 'Product Group',
    'decoration.productList.settings.newGroup': 'New Group {number}',
    'decoration.productList.settings.group': 'Group',
    'decoration.productList.settings.filter.onlyActive': 'only view active products',
};
