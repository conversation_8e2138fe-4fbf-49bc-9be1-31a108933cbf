import { compressImage } from '@/utils/fileUtils';
import { PlusOutlined } from '@ant-design/icons';
import { useIntl } from '@umijs/max';
import { Button, message, Modal, Upload } from 'antd';
import { useState } from 'react';
import { finishDelivery, uploadImages } from '../../services';

interface FinishDeliveryModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  deliveryId: string;
}

const FinishDeliveryModal: React.FC<FinishDeliveryModalProps> = ({
  visible,
  onClose,
  onSuccess,
  deliveryId
}) => {
  const intl = useIntl();
  const t = (id: string, ...rest: any[]) => intl.formatMessage({ id }, ...rest);

  const [imageUrl, setImageUrl] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const handleUploadChange = (info: any) => {
    if (info.file.status === 'done') {
      const url = info.file?.response?.data?.[0];
      if (url) {
        setImageUrl(url);
      } else {
        message.error(info.file?.response?.msg || t('common.message.upload.failed'));
      }
    } else if (info.file.status === 'error') {
      message.error(t('common.message.upload.failed'));
    }
  };

  const handleFinish = async () => {
    if (!imageUrl) {
      message.error(t('stocks.delivery.finishModal.pleaseUploadImage'));
      return;
    }

    setLoading(true);
    try {
      // 先上传图片
      await uploadImages({
        id: deliveryId,
        images: [imageUrl]
      });

      // 然后完成配送
      await finishDelivery({ id: deliveryId });

      message.success(t('stocks.delivery.finishModal.success'));
      onSuccess();
      handleClose();
    } catch (error) {
      message.error(t('common.message.operation.failed'));
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setImageUrl('');
    onClose();
  };

  const uploadButton = (
    <div className="flex flex-col items-center justify-center h-32 text-gray-400">
      <PlusOutlined className="text-2xl mb-2" />
      <div>{t('stocks.delivery.finishModal.uploadImage')}</div>
    </div>
  );

  return (
    <Modal
      title={t('stocks.delivery.finishModal.title')}
      open={visible}
      onCancel={handleClose}
      destroyOnClose
      width={400}
      footer={[
        <Button key="cancel" onClick={handleClose}>
          {t('common.button.cancel')}
        </Button>,
        <Button
          key="confirm"
          type="primary"
          loading={loading}
          onClick={handleFinish}
        >
          {t('stocks.delivery.finishModal.confirm')}
        </Button>
      ]}
    >
      <div className="py-4">
        <div className="mb-4 p-3 bg-gray-50 rounded border border-gray-200">
          <Upload
            name="file"
            listType="picture-card"
            showUploadList={false}
            action="/apigateway/public/upload/object/batch"
            beforeUpload={(file) => compressImage(file)}
            onChange={handleUploadChange}
            accept=".jpg,.png,.jpeg,.gif,.webp"
            className="w-full"
          >
            {imageUrl ? (
              <img src={imageUrl} alt="delivery" className="w-full h-full object-cover" />
            ) : (
              uploadButton
            )}
          </Upload>
        </div>
      </div>
    </Modal>
  );
};

export default FinishDeliveryModal;
