﻿/**
 * @name umi 的路由配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn，注意：此处值为字符串，需要到menuIconMap中再配置和icon组件到转换映射
 * @param hideInMenu 是否在菜单项中隐藏
 * @param whitePath 路由白名单，不受后端权限控制
 * @doc https://umijs.org/docs/guides/routes
 * 注意：在服务端动态下发menu的场景中，此路由设置中的name、icon等配置会遵循远程数据的配置
 */

export interface RouteType {
  path: string;
  layout?: boolean;
  hideInMenu?: boolean;
  component?: string;
  icon?: string;
  name?: string;
  routes?: RouteType[];
  access?: string;
  whitePath?: boolean;
}

export default [
  { path: '/', redirect: '/home', hideInMenu: true, whitePath: true },
  {
    name: 'Login',
    path: '/login',
    layout: false,
    hideInMenu: true,
    whitePath: true,
    component: './common/login',
  },
  {
    name: '首页',
    icon: 'DashboardOutlined',
    path: '/home',
    whitePath: true,
    access: 'canAccess',
    component: './home',
    authority: 'HOME',
  },
  {
    name: '客户',
    icon: 'UserOutlined',
    path: '/customer',
    access: 'canAccess',
    authority: 'CUSTOMER',
    routes: [
      {
        path: '/customer/list',
        name: '客户管理',
        component: './customer/list',
        access: 'canAccess',
        authority: 'CUSTOMER_LIST',
      },
      {
        path: '/customer/property',
        name: '客户属性管理',
        component: './customer/property',
        access: 'canAccess',
        authority: 'CUSTOMER_PROPERTY',
      },
    ],
  },
  {
    name: '商品',
    icon: 'ShoppingOutlined',
    path: '/goods',
    access: 'canAccess',
    routes: [
      {
        path: '/goods/list',
        name: '商品管理',
        component: './goods/list',
        access: 'canAccess',
      },
      {
        path: '/goods/property',
        name: '商品属性管理',
        component: './goods/property',
        access: 'canAccess',
      },
    ],
  },
  {
    name: '销售',
    icon: 'TagsOutlined',
    path: '/sales',
    access: 'canAccess',
    routes: [
      {
        path: '/sales/order/edit',
        name: '销售开单',
        component: './sales/order/edit',
        access: 'canAccess',
      },
      {
        path: '/sales/order/list',
        name: '销售单管理',
        component: './sales/order/list',
        access: 'canAccess',
      },
      {
        path: '/sales/order/detail',
        name: '销售单详情',
        hideInMenu: true,
        component: './sales/order/detail',
        access: 'canAccess',
      },
      {
        path: '/sales/returns/list',
        name: '销售退货管理',
        component: './sales/returns/list',
        access: 'canAccess',
      },
      {
        path: '/sales/returns/operation',
        name: '销售退货',
        component: './sales/returns/operation',
        access: 'canAccess',
      },
      {
        path: '/sales/returns/detail',
        name: '销售退货详情',
        hideInMenu: true,
        component: './sales/returns/detail',
        access: 'canAccess',
      }
    ],
  },
  {
    name: '采购',
    icon: 'ShoppingCartOutlined',
    path: '/purchase',
    access: 'canAccess',
    routes: [
      {
        path: '/purchase/external',
        name: '采购开单',
        component: './purchase/external',
        access: 'canAccess',
      },
      {
        path: '/purchase/list',
        name: '采购单管理',
        component: './purchase/list',
        access: 'canAccess',
      },
      {
        path: '/purchase/detail',
        name: '采购单详情',
        hideInMenu: true,
        component: './purchase/detail',
        access: 'canAccess',
      },
      {
        path: '/purchase/returns/list',
        name: '采购退货管理',
        component: './purchase/returns/list',
        access: 'canAccess',
      },
      {
        path: '/purchase/returns/detail',
        name: '采购退货详情',
        hideInMenu: true,
        component: './purchase/returns/detail',
        access: 'canAccess',
      },
      {
        path: '/purchase/returns/operation',
        name: '采购退货',
        component: './purchase/returns/operation',
        access: 'canAccess',
      },
      {
        path: '/purchase/supplier/list',
        name: '供应商管理',
        component: './purchase/supplier/list',
        access: 'canAccess',
      },
      {
        path: '/purchase/stockUp/list',
        name: '补货建议',
        component: './purchase/stockUp/list',
        access: 'canAccess',
      },
      {
        path: '/purchase/stockUp/detail',
        name: '补货建议详情',
        hideInMenu: true,
        component: './purchase/stockUp/detail',
        access: 'canAccess',
      },
    ],
  },
  {
    name: '仓储',
    icon: 'BankOutlined',
    path: '/stocks',
    access: 'canAccess',
    routes: [
      {
        path: '/stocks/warehouse',
        name: '仓库管理',
        component: './stocks/warehouse/list',
        access: 'canAccess',
      },
      {
        path: '/stocks/location',
        name: '库位管理',
        component: './stocks/location/list',
        access: 'canAccess',
      },
      {
        path: '/stocks/output',
        name: '出库管理',
        component: './stocks/output/list',
        access: 'canAccess',
      },
      {
        path: '/stocks/output/detail',
        name: '出库详情',
        hideInMenu: true,
        component: './stocks/output/detail',
        access: 'canAccess',
      },
      {
        path: '/stocks/input',
        name: '入库管理',
        component: './stocks/input/list',
        access: 'canAccess',
      },
      {
        path: '/stocks/input/detail',
        name: '入库详情',
        hideInMenu: true,
        component: './stocks/input/detail',
        access: 'canAccess',
      },
      {
        path: '/stocks/transfer',
        name: '调拨管理',
        component: './stocks/transfer/list',
        access: 'canAccess',
      },
      {
        path: '/stocks/transfer/detail',
        name: '调拨详情',
        hideInMenu: true,
        component: './stocks/transfer/detail',
        access: 'canAccess',
      },
      {
        path: '/stocks/transfer/operation',
        name: '新增调拨',
        hideInMenu: true,
        component: './stocks/transfer/operation',
        access: 'canAccess',
      },
      {
        path: '/stocks/check',
        name: '盘点管理',
        component: './stocks/check/list',
        access: 'canAccess',
      },
      {
        path: '/stocks/check/operation',
        name: '新增盘点',
        component: './stocks/check/operation',
        hideInMenu: true,
        access: 'canAccess',
      },
      {
        path: '/stocks/check/detail',
        name: '盘点详情',
        hideInMenu: true,
        component: './stocks/check/detail',
        access: 'canAccess',
      },
      {
        path: '/stocks/inventory',
        name: '库存管理',
        component: './stocks/inventory/list',
        access: 'canAccess',
      },
      {
        path: '/stocks/delivery/list',
        name: '配送',
        component: './stocks/delivery/list',
        access: 'canAccess',
      },
      {
        path: '/stocks/delivery/detail',
        name: '配送详情',
        component: './stocks/delivery/detail',
        access: 'canAccess',
        hideInMenu: true,
      },
    ],
  },
  {
    name: '财务',
    icon: 'DollarOutlined',
    path: '/finance',
    access: 'canAccess',
    routes: [
      {
        path: '/finance/collection',
        name: '应收管理',
        component: './finance/collection',
        access: 'canAccess',
      },
      {
        path: '/finance/receive',
        name: '收款',
        component: './finance/receive',
        access: 'canAccess',
      },
      {
        path: '/finance/receive/add',
        name: '新增收款单',
        component: './finance/receive/AddReceived',
        access: 'canAccess',
        hideInMenu: true,
      },
      {
        path: '/finance/payment',
        name: '应付管理',
        component: './finance/payment',
        access: 'canAccess',
      },
      {
        path: '/finance/supplierPayment',
        name: '付款',
        component: './finance/supplierPayment',
        access: 'canAccess',
      },
      // 二期需求
      {
        path: '/finance/flow',
        name: '资金流水管理',
        component: './finance/flow',
      },
      {
        path: '/finance/tag',
        name: '财务属性管理',
        component: './finance/tag',
      },
      {
        path: '/finance/customer',
        name: '账户管理',
        component: './finance/customer',
        access: 'canAccess',
      },
      {
        path: '/finance/cost',
        name: '其他收支管理',
        component: './finance/cost',
      },
      {
        path: '/finance/accountFlow',
        name: '预收明细',
        component: './finance/accountFlow',
      },
      {
        path: '/finance/otherRelated',
        name: '其他往来单位',
        component: './finance/otherRelated',
      }
    ],
  },
  {
    // 商城后台管理
    name: '商城管理',
    icon: 'ShopOutlined',
    path: '/shop',
    access: 'canAccess',
    routes: [
      {
        path: '/shop/topic/decoration',
        name: '装修',
        component: './shop/topic/decoration',
        access: 'canAccess',
        hideInMenu: true,
      },
      {
        path: '/shop/topic/list',
        name: '商城装修',
        component: './shop/topic/list',
        access: 'canAccess',
      },
      {
        path: '/shop/activity/list',
        name: '活动管理',
        component: './shop/activity/list',
        access: 'canAccess',
      },
      {
        path: '/shop/activity/edit',
        name: '活动编辑',
        component: './shop/activity/edit',
        access: 'canAccess',
        hideInMenu: true,
      },
      {
        path: '/shop/coupon/list',
        name: '优惠券管理',
        component: './shop/coupon/list',
        access: 'canAccess',
      },
      {
        path: '/shop/coupon/edit',
        name: '编辑优惠券',
        component: './shop/coupon/edit',
        access: 'canAccess',
        hideInMenu: true,
      },
      {
        path: '/shop/coupon/record',
        name: '优惠券使用记录',
        component: './shop/coupon/record',
        access: 'canAccess',
      },
      {
        path: '/shop/category',
        name: '商城目录',
        component: './shop/category',
        access: 'canAccess',
      },
    ]
  },
  {
    name: '报表',
    icon: 'LineChartOutlined',
    path: '/report',
    routes: [
      {
        path: '/report/finance',
        name: '财务报表',
        component: './report/finance',
        access: 'canAccess',
      },
      {
        path: '/report/purchase',
        name: '采购报表',
        component: './report/purchase',
        access: 'canAccess',
      },
      {
        path: '/report/inventory',
        name: '库存报表',
        component: './report/inventory',
        access: 'canAccess',
      },
      {
        path: '/report/sales',
        name: '销售报表',
        component: './report/sales',
        access: 'canAccess',
      },
    ],
  },
  {
    name: '系统',
    icon: 'SettingOutlined',
    path: '/system',
    access: 'canAccess',
    routes: [
      {
        path: '/system/user',
        name: '员工管理',
        component: './system/user/list',
        access: 'canAccess',
      },
      {
        path: '/system/store',
        name: '门店管理',
        component: './system/store/list',
        access: 'canAccess',
      },
      {
        path: '/system/role',
        name: '角色管理',
        component: './system/role/list',
        access: 'canAccess',
      },
      {
        path: '/system/job',
        name: '导入导出',
        component: './system/job/list',
        access: 'canAccess',
      },
      {
        path: '/system/config',
        name: '系统设置',
        component: './system/config',
        access: 'canAccess',
      },
      {
        path: '/system/message',
        name: '消息管理',
        component: './system/message',
        access: 'canAccess',
        authority: 'SYSTEM_MESSAGE',
      },
    ],
  },
  {
    name: '一体系系统探针',
    path: '/actuator/health',
    hideInMenu: true,
    layout: false,
    whitePath: true,
    component: './common/monitor',
  },
  {
    name: '打印预览/打印',
    path: '/print',
    hideInMenu: true,
    layout: false,
    whitePath: true,
    component: './common/print',
  },
  {
    name: '无权限',
    path: '/403',
    hideInMenu: true,
    whitePath: true,
    component: './common/403',
  },
  {
    path: '*',
    hideInMenu: true,
    whitePath: true,
    component: './common/404',
  },
] as RouteType[];
