import { Button, ConfigProvider, Empty, Tabs } from 'antd';
import LeftTitle from '@/components/LeftTitle';
import FunProTable from '@/components/common/FunProTable';
import type { GoodsEntity } from '@/pages/goods/list/types/GoodsEntity.entity';
import { GeneralGroupGoodsColumns } from '@/pages/goods/list/config/GeneralGroupGoodsColumns';
import React, { useEffect, useState } from 'react';
import { IcItemGroupEntity } from '@/pages/goods/list/types/IcItemGroupType';
import { ItemGroupBase } from '@/pages/goods/list/types/item.group.base';
import { useIntl } from '@@/exports';
import GeneralGroupCreateDrawer, {
  GeneralGroupCreateDrawerProps,
} from '@/pages/goods/list/components/GeneralGroupCreateDrawer';
import { getItemGroupBase, queryIcItemGroupList } from '@/pages/goods/list/services';

export interface GroupInfoProps {
  groupId?: string;
  groupIds?: string[];
  refresh?: () => void;
  onClose?: () => void;
}

export default function GroupInfo(props: GroupInfoProps) {
  const { groupId, groupIds, refresh, onClose } = props;
  const [groups, setGroups] = useState<IcItemGroupEntity[][]>([]);
  const [tabKey, setTabKey] = useState<string>();
  const [bases, setBases] = useState<ItemGroupBase[]>([]);
  const intl = useIntl();

  const [createGeneralGroupModalProps, setCreateGeneralGroupModalProps] =
    useState<GeneralGroupCreateDrawerProps>({
      visible: false,
    });

  useEffect(() => {
    if (groupId) {
      setTabKey(groupId);
    } else {
      setTabKey(groupIds?.[0]);
    }
  }, [groupId, groupIds]);

  const queryDetail = () => {
    const promise1: any[] = [];
    const promise2: any[] = [];
    groupIds?.forEach((item) => {
      promise1.push(
        queryIcItemGroupList({
          groupId: item,
          isFetchBrand: true,
          isFetchBrandPart: true,
          isFetchOe: true,
          isFetchCategory: true,
        }),
      );
      promise2.push(getItemGroupBase({ groupId: item }));
    });
    Promise.all(promise1).then((res) => {
      if (res) {
        setGroups(res);
      }
    });
    Promise.all(promise2).then((res) => {
      if (res) {
        setBases(res);
      }
    });
  };

  useEffect(() => {
    if (groupIds?.length) {
      queryDetail();
    }
  }, [groupIds]);

  if (bases.length === 0 && groups.length === 0) {
    return (
      <div className="py-20">
        <Empty />
      </div>
    );
  }

  return (
    <div>
      <ConfigProvider
        theme={{
          components: {
            Tabs: {
              horizontalMargin: '0',
            },
          },
        }}
      >
        <Tabs
          type="card"
          activeKey={tabKey}
          onChange={setTabKey}
          items={bases.map((item, index) => ({
            label: item.name,
            key: item.id.toString(),
            children: (
              <div className="bg-white pt-4">
                <LeftTitle
                  title={intl.formatMessage({
                    id: 'goods.general.group.goods.set',
                  })}
                  extra={
                    <Button
                      type={'link'}
                      onClick={() => {
                        setCreateGeneralGroupModalProps({
                          id: item.id.toString(),
                          visible: true,
                        });
                      }}
                    >
                      {intl.formatMessage({
                        id: 'goods.general.group.edit',
                      })}
                    </Button>
                  }
                  className="px-7"
                />
                <FunProTable<GoodsEntity, any>
                  rowKey="itemId"
                  pagination={false}
                  // @ts-ignore
                  dataSource={groups[index]}
                  options={false}
                  scroll={{ x: 400 }}
                  search={false}
                  columns={GeneralGroupGoodsColumns({
                    mainItemId: groups[index]?.find((item) => item.isMain)?.itemId,
                  })}
                />
              </div>
            ),
          }))}
        />
      </ConfigProvider>
      <GeneralGroupCreateDrawer
        {...createGeneralGroupModalProps}
        onClose={() => {
          setCreateGeneralGroupModalProps({ visible: false });
        }}
        onSuccess={() => {
          setTimeout(() => {
            onClose?.();
            refresh?.();
          }, 1000);
        }}
      />
    </div>
  );
}
