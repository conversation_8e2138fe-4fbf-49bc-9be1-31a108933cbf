import { CustomerSaveEntity } from '@/pages/customer/list/types/CustomerSaveEntity';
import {
  DeliveryMethod,
  useDeliveryMethodOptions,
} from '@/pages/sales/order/edit/types/DeliveryMethod';
import { ProFormDependency } from '@ant-design/pro-components';
import { ProFormSelect, ProFormText } from '@ant-design/pro-form';
import { useIntl } from '@umijs/max';
import { useState } from 'react';
import { accountListQuerySimple } from '@/pages/system/user/services';

export interface DeliveryFormData {
  cstDetail?: CustomerSaveEntity;
}

const DeliveryForm = (props: DeliveryFormData) => {
  const intl = useIntl();
  const { cstDetail } = props;
  const deliveryMethodOptions = useDeliveryMethodOptions();

  // 获取配送地址列表
  const getAddressList = () => {
    const options: any = [];
    if (cstDetail?.addresses?.length) {
      cstDetail?.addresses.forEach((item, index) => {
        options.push({
          label: `${item.provinceName}${item.cityName}${item.prefectureName}${item.address}`,
          value: item.id,
        });
      });
    }
    return options;
  };

  return (
    <>
      <ProFormSelect
        options={deliveryMethodOptions}
        name="deliveryMethod"
        label={
          <span className="font-semibold">
            {intl.formatMessage({ id: 'sales.order.edit.deliveryMethod' })}
          </span>
        }
      />
      <ProFormDependency name={['deliveryMethod']}>
        {({ deliveryMethod }) => {
          if ([DeliveryMethod.MERCHANT_DELIVERY].includes(deliveryMethod)) {
            return (
              <>
                <ProFormSelect
                  name="addressId"
                  placeholder={intl.formatMessage({ id: 'sales.order.edit.selectDeliveryAddress' })}
                  options={getAddressList()}
                />
                <ProFormSelect
                  name="deliveryMan"
                  fieldProps={{
                    showSearch: true,
                    fieldNames: { label: 'name', value: 'id' },
                  }}
                  // @ts-ignore
                  request={accountListQuerySimple}
                />
              </>
            );
          }
          {
            if ([DeliveryMethod.EXPRESS_LOGISTICS].includes(deliveryMethod)) {
              return (
                <>
                  <ProFormSelect
                    name="addressId"
                    placeholder={intl.formatMessage({
                      id: 'sales.order.edit.selectDeliveryAddress',
                    })}
                    options={getAddressList()}
                  />
                  <ProFormText
                    name="logisticsCompanyName"
                    placeholder={intl.formatMessage({
                      id: 'sales.order.edit.inputLogisticsCompany',
                    })}
                  />
                  <ProFormText
                    name="logisticsNo"
                    placeholder={intl.formatMessage({
                      id: 'sales.order.edit.inputLogisticsNumber',
                    })}
                  />
                </>
              );
            }
          }
        }}
      </ProFormDependency>
    </>
  );
};

export default DeliveryForm;
